{"name": "monomer-uni-app", "version": "1.0.0", "scripts": {"dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "node scripts/build.h5.mjs", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020420240722002", "@dcloudio/uni-app-plus": "3.0.0-4020420240722002", "@dcloudio/uni-components": "3.0.0-4020420240722002", "@dcloudio/uni-h5": "3.0.0-4020420240722002", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722002", "@dcloudio/uni-mp-baidu": "3.0.0-4020420240722002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020420240722002", "@dcloudio/uni-mp-lark": "3.0.0-4020420240722002", "@dcloudio/uni-mp-qq": "3.0.0-4020420240722002", "@dcloudio/uni-mp-toutiao": "3.0.0-4020420240722002", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722002", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722002", "clipboard": "^2.0.11", "crypto-js": "^3.1.9-1", "dayjs": "^1.11.3", "lodash-es": "^4.17.21", "pinia": "2.0.36", "pinia-plugin-persistedstate": "^4.4.1", "qs": "6.11.0", "side-channel": "^1.0.6", "vconsole": "^3.14.6", "vue": "3.4.21", "vue-i18n": "^9.2.2", "weixin-js-sdk": "^1.6.0", "z-paging": "^2.3.8"}, "devDependencies": {"@dcloudio/types": "^3.0.13", "@dcloudio/uni-automator": "3.0.0-4020420240722002", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722002", "@dcloudio/uni-stacktracey": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722002", "@rushstack/eslint-patch": "^1.1.4", "@types/lodash-es": "^4.17.6", "@types/node": "^18.7.16", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "autoprefixer": "^10.4.8", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.4.0", "execa": "^6.1.0", "fs-extra": "^10.1.0", "postcss": "^8.4.16", "postcss-rem-to-responsive-pixel": "^5.1.3", "prettier": "^2.7.1", "sass": "1.54.5", "tailwindcss": "^3.1.8", "typescript": "^4.7.4", "vite": "5.2.8", "weapp-tailwindcss-webpack-plugin": "^1.7.0"}}