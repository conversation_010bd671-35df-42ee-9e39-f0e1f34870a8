# MONOMER-UNI-APP 移动端应用

基于 Vue3 + TypeScript + UniApp + uvUI + Tailwind CSS 的跨平台移动端应用

## 🚀 技术栈

- **框架**: Vue 3.4.21 + TypeScript 4.7.4
- **跨平台**: UniApp 3.0.0
- **UI组件库**: uvUI (uview-ui)
- **样式**: Tailwind CSS 3.1.8
- **状态管理**: Pinia 2.0.36
- **构建工具**: Vite 5.2.8
- **包管理**: npm

## 📱 支持平台

- 微信小程序 (mp-weixin)
- H5 移动端
- App (iOS/Android)
- 支付宝小程序 (mp-alipay)
- 百度小程序 (mp-baidu)
- 字节跳动小程序 (mp-toutiao)
- QQ小程序 (mp-qq)
- 快手小程序 (mp-kuaishou)
- 飞书小程序 (mp-lark)
- 快应用 (quickapp-webview)

## 🛠️ 开发环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 📦 安装依赖

```bash
npm install --registry=https://registry.npmmirror.com/
```

## 🚀 启动项目

### 开发模式

```bash
# 微信小程序
npm run dev:mp-weixin

# H5 移动端
npm run dev:h5

# App 开发
npm run dev:app

# 支付宝小程序
npm run dev:mp-alipay

# 百度小程序
npm run dev:mp-baidu

# 字节跳动小程序
npm run dev:mp-toutiao

# QQ小程序
npm run dev:mp-qq

# 快手小程序
npm run dev:mp-kuaishou

# 飞书小程序
npm run dev:mp-lark

# 快应用
npm run dev:quickapp-webview
```

### 生产构建

```bash
# 微信小程序
npm run build:mp-weixin

# H5 移动端
npm run build:h5

# App 构建
npm run build:app

# 其他平台构建命令类似
```

## 📁 项目结构

```
src/
├── api/                    # API 接口
│   ├── account.ts         # 账户相关接口
│   ├── app.ts            # 应用配置接口
│   ├── home.ts           # 首页接口
│   ├── news.ts           # 资讯接口
│   ├── todo.ts           # 待办事项接口
│   ├── user.ts           # 用户相关接口
│   └── ...
├── components/            # 公共组件
│   ├── app/              # 应用级组件
│   ├── widgets/          # 业务组件
│   └── ...
├── enums/                # 枚举定义
├── hooks/                # 组合式函数
├── pages/                # 页面文件
│   ├── index/           # 首页
│   ├── todo/            # 待办事项
│   ├── news/            # 资讯
│   ├── user/            # 个人中心
│   ├── login/           # 登录
│   └── ...
├── pkg-todo/            # 待办事项分包
├── plugins/             # 插件配置
│   └── modules/         # 插件模块
├── router/              # 路由配置
├── stores/              # 状态管理
│   ├── app.ts          # 应用状态
│   └── user.ts         # 用户状态
├── styles/              # 样式文件
│   ├── index.scss      # 主样式
│   ├── tailwind.css    # Tailwind CSS
│   └── ...
├── utils/               # 工具函数
│   ├── request/        # 请求封装
│   ├── auth.ts         # 认证工具
│   ├── cache.ts        # 缓存工具
│   └── ...
├── uni_modules/         # UniApp 组件库
├── App.vue             # 应用入口
├── main.ts             # 主入口
├── manifest.json       # 应用配置
└── pages.json          # 页面配置
```

## 🎨 主要功能

### 页面功能
- **首页**: 应用主页面
- **待办事项**: 任务管理功能
- **资讯**: 新闻资讯浏览
- **个人中心**: 用户信息管理
- **登录/注册**: 用户认证
- **搜索**: 内容搜索功能
- **设置**: 个人设置

### 技术特性
- **响应式设计**: 支持多平台适配
- **状态管理**: 基于 Pinia 的状态管理
- **路由管理**: 自动路由生成
- **请求封装**: 统一的 HTTP 请求处理
- **缓存管理**: 本地数据缓存
- **组件化**: 高度组件化的架构
- **TypeScript**: 完整的类型支持

## 🔧 配置说明

### 环境变量
项目支持多环境配置，主要环境变量：
- `VITE_APP_BASE_URL`: API 基础地址
- `VITE_IS_H5`: 是否为 H5 环境
- `VITE_IS_TOC`: 是否为 TOC 环境

### 样式配置
- 使用 Tailwind CSS 进行样式开发
- 支持 rpx 单位自动转换
- 自定义颜色主题配置

### 请求配置
- 统一的请求拦截器
- 自动 token 管理
- 错误处理机制
- 支持请求重试

## 📚 开发指南

### 组件开发
- 使用 Vue 3 Composition API
- 遵循 TypeScript 类型规范
- 组件放置在 `src/components/` 目录

### 页面开发
- 页面文件放置在 `src/pages/` 目录
- 在 `pages.json` 中配置页面路由
- 支持页面级别的权限控制

### API 开发
- API 接口放置在 `src/api/` 目录
- 使用统一的请求工具 `src/utils/request/`
- 支持请求和响应拦截器

### 状态管理
- 使用 Pinia 进行状态管理
- Store 文件放置在 `src/stores/` 目录
- 支持持久化存储

## 🔗 相关链接

- [uvUI 官网]: https://www.uvui.cn/components/intro.html
- [UniApp 官网]: https://uniapp.dcloud.net.cn/
- [Vue 3 文档]: https://vuejs.org/
- [Tailwind CSS]: https://tailwindcss.com/
- [Pinia 文档]: https://pinia.vuejs.org/

## 📄 许可证

本项目采用 MIT 许可证
