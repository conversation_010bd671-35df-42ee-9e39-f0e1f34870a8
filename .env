#是否是TOC应用 -> 是否是C端用户 -> B端用户
VITE_IS_TOC=false

#是否是H5模式
VITE_IS_H5=false

#是否是微服务架构
VITE_IS_MICRO=false

# 前端加密密钥
VITE_PWD_ENC_KEY='canpancanpancanp'

# OAUTH2 APP模式客户端信息 (不需要验证码，微信登录)
VITE_OAUTH2_APP_CLIENT='mini:mini'

# OAUTH2 APP模式客户端信息 (账号密码登录，短信登录)
VITE_OAUTH2_MOBILE_CLIENT='app:app'

# 请求 网关地址
# VITE_APP_BASE_URL='https://saas-monomer.canpanscp.com/api'
VITE_APP_BASE_URL='https://ayn.canpanscp.com/api'

# 动态获取 package.json 中的 version
VITE_APP_VERSION=$npm_package_version
