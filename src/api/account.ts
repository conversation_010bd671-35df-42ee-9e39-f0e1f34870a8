import request from '@/utils/request'
import { encryption, encryptionBase64 } from '@/utils/util'
import qs from 'qs'

const basicAuth = 'Basic ' + encryptionBase64(import.meta.env.VITE_OAUTH2_APP_CLIENT)

console.log('basicAuth', basicAuth)

const basicMobileAuth = 'Basic ' + encryptionBase64(import.meta.env.VITE_OAUTH2_MOBILE_CLIENT)

console.log('basicMobileAuth', basicMobileAuth)

// 由于是单机部署模式 -> 统一使用了admin
// const projectName = '/auth'
const projectName = '/admin'

/**
 * B端用户 - 用户名/密码登录
 * 登录 - 商家用户名/密码登录
 * @param data
 */
export const accountLogin = (data: any) => {
  const { username, randomStr, grant_type, scope } = data

  const params = {
    username: username,
    randomStr: randomStr || 'noCode',
    grant_type: grant_type || 'password',
    scope: scope || 'server'
  }
  const encPassword = encryption(data.password, import.meta.env.VITE_PWD_ENC_KEY)
  return request.post(
    {
      url: `${projectName}/oauth2/token?` + qs.stringify(params),
      data: { password: encPassword },
      header: {
        Authorization: basicMobileAuth,
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false }
  )
}

// B端用户 - 手机验证码登录
export function mobileLogin({ mobile, code }: Record<string, any>) {
  const form = {
    mobile: `SMS@${mobile}`,
    code,
    grant_type: 'mobile',
    scope: 'server'
  }

  return request.post(
    {
      url: `${projectName}/oauth2/token?` + qs.stringify(form),
      header: {
        Authorization: basicMobileAuth,
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false }
  )
}

/**
 * C端用户 - 微信一键登录
 * 一键登录
 * @param {} data
 * @returns
 */
export function appletLogin(data) {
  const { appId, jsCode, phoneNum, code, encryptedData, iv } = data
  console.log(
    'appletLogin',
    `${projectName}/oauth2/token?mobile=APP-MINI@${appId}@${jsCode}@${encryptedData}@${iv}&grant_type=mobile&scope=server`
  )
  return Promise.reject()
  // return request.post(
  //   {
  //     url: `${projectName}/oauth2/token?mobile=APP-MINI@${appId}@${jsCode}@${encryptedData}@${iv}&grant_type=mobile&scope=server`,
  //     header: {
  //       Authorization: `Basic cGlnOnBpZw==`,
  //     },
  //   },
  //   { withToken: false }
  // )
}

// // 账号密码登录
// export function accountLogin(data: Record<string, any>) {
//   data.grant_type = 'password'
//   data.scope = 'server'

//   return request.post(
//     {
//       url: "/auth/oauth2/token?" + qs.stringify(data),
//       header: {
//         Authorization: basicAuth,
//         "content-type": "application/x-www-form-urlencoded",
//       },
//     },
//     { withToken: false }
//   );
// }

// 微信小程序登录

export function mnpLogin(code: string) {
  const form = {
    mobile: `APP-MINI@${code}`,
    code: code,
    grant_type: 'mobile',
    scope: 'server'
  }

  return request.post(
    {
      url: '/auth/oauth2/token?' + qs.stringify(form),
      header: {
        Authorization: basicAuth, //"mini:mini"
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false }
  )
}

//注册
export function register(data: Record<string, any>) {
  return request.post({ url: '/app/appuser/register', data })
}

//忘记密码
export function forgotPassword(data: Record<string, any>) {
  return request.post({ url: '/app/login/forgotPassword', data })
}

//向微信请求code的链接
export function getWxCodeUrl() {
  return request.get({
    url: '/app/login/oaCodeUrl',
    data: { url: location.href }
  })
}

// 公众号登录
export function OALogin() {
  return
}

export function getJsCode(obj?: Object) {
  return request.get(
    {
      url: `${projectName}/user/jsCode?` + qs.stringify(obj),
      header: {
        Authorization: basicMobileAuth,
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false, withTenant: false }
  )
}

export function postMiniOpenid(obj?: Object) {
  return request.post({
    url: `${projectName}/user/update/miniOpenid`,
    data: obj,
    header: {
      Authorization: basicMobileAuth
    }
  })
}
