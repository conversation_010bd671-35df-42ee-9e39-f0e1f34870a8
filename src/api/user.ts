import request from '@/utils/request'
import qs from 'qs'

const projectName = '/admin'
/**
 * 查询租户列表
 * @param {} data username phone openid
 * @returns
 */
export function getTenantsByLogin(data?: Object) {
  return request.post(
    {
      url: `${projectName}/tenant/login_tenant_list`,
      header: {
        Authorization: `Basic Y2FuOmNhbg==`,
        'content-type': 'application/json'
      },
      data
    },
    { withToken: false, withTenant: false }
  )
}
/**
 * 查询租户列表
 * @param {} data
 * @returns
 */
export function getTenantListByAppId(data) {
  const { appId } = data
  return request.get({
    url: `${projectName}/appsocial/appIdUserInfo/${apppId}`
  })
}

export function getUserCenter(header: any) {
  const url = import.meta.env.VITE_IS_TOC === 'true' ? `${projectName}/appuser/info` : `${projectName}/user/info`
  return request.get({ url, header })
}

// 个人编辑
export function userEdit(data: any) {
  return request.put({ url: '/app/appuser/edit', data })
}

// 绑定手机
export function userBindMobile(data: any, header?: any) {
  return request.post({ url: '/app/user/bindMobile', data, header }, { isAuth: true })
}

export function userChangePwd(data: any) {
  return request.put({ url: '/app/appuser/edit', data })
}

// 绑定小程序
export function mnpAuthBind(data: any) {
  return request.post({ url: '/app/user/bindMnp', data })
}

// 绑定公众号
export function oaAuthBind(data: any) {
  return request.post({ url: '/app/user/bindOa', data })
}

//更新微信小程序头像昵称
export function updateUser(data: Record<string, any>, header: any) {
  return request.post({ url: '/app/user/updateUser', data, header })
}

// 获取secret 用于切换组织
export function getSecret(data: any) {
  return request.post({ url: `/app/sysMessage/send/secret?${qs.stringify(data)}` })
}
// 获取token 用于切换组织
export function getToken(data: any) {
  return request.post(
    {
      url: `/app/oauth2/token?${qs.stringify(data)}`,
      header: {
        Authorization: `Basic YXBwOmFwcA==`,
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false }
  )
}
// 退出token
export function logout() {
  return request.delete({ url: `/app/token/logout` })
}
// 获取租户列表
export function loginTenantList(data: any) {
  return request.post(
    {
      url: `/app/tenant/login_tenant_list`,
      header: {
        Authorization: `Basic Y2FuOmNhbg==`,
        'content-type': 'application/json'
      },
      data
    },
    { withTenant: false }
  )
}

// 获取当前登录的用户信息
// https://yapi.ops.yunlizhi.cn/project/1018/interface/api/123468
export function userDetailInfo(data: any) {
  return request.get({ url: `/api/app/user/userInfo` })
}

// 通过原密码修改密码
export function upsertUserPwd(data: any) {
  return request.put({ url: '/app/user/personal/password', data })
}

// 通过短信验证码修改密码
export function upsertUserPwdBySms(data: any) {
  return request.put({ url: '/app/user/phone/password', data }, { withTenant: false, withToken: false })
}

// 校验token是否过期
export function checkToken(data: any) {
  return request.get(
    {
      url: `/app/token/check_token?${qs.stringify(data)}`,
      header: {
        Authorization: `Basic YXBwOmFwcA==`,
        'content-type': 'application/x-www-form-urlencoded'
      }
    },
    { withToken: false }
  )
}
