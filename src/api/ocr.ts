import request from '@/utils/request'

// 获取OCR识别的内容 腾讯服务
export function ocrIdentifyTencent(data: any) {
  console.log('ocrIdentify', data)
  return request.post(
    { url: '/app/ocr/ocrIdentifyTencent', data },
    {
      ignoreCancel: true
    }
  )
}

// 获取OCR识别的内容
export function ocrIdentify(data: any) {
  console.log('ocrIdentify', data)
  return request.post(
    { url: '/app/ocr/ocrIdentify', data },
    {
      ignoreCancel: true
    }
  )
}

// 获取配置字段
export function bizCustomFields(data: any) {
  return request.post({ url: '/app/bizCustomFields/list', data })
}
