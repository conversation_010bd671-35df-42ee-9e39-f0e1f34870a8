import request from '@/utils/request'
import qs from 'qs'

// 查询当前登录用户的待办任务
export function queryMineTask(data: any) {
  return request.post({ url: '/admin/app/audit/queryTaskPage', data }, { ignoreCancel: true })
}

// 批量完成任务
export function completeTaskBatch(data: any) {
  // return request.post({ url: '/admin/app/audit/completeTaskBatch', data })
  return request.post({ url: '/app/task/batchCompleteTask', data })
}

// 批量完成任务-简单版
export function easyBatchCompleteTask(data: any) {
  // return request.post({ url: '/admin/app/audit/completeTaskBatch', data })
  return request.post({ url: '/app/task/easyBatchCompleteTask', data })
}

// 完成任务
export function completeTask(data: any) {
  return request.post({ url: '/admin/app/audit/completeTask', data })
}

// 查询任务
export function queryTask(data: any) {
  return request.get({ url: '/admin/app/audit/queryTask', data })
}

// 审批记录
// https://shan.canpanscp.com/api/admin/process-instance/formatStartNodeShow
export function formatStartNodeShow(data: any) {
  return request.post({ url: '/admin/process-instance/formatStartNodeShow', data })
}

// 查看审批详情
export function queryTaskDetail(data: any) {
  return request.get({ url: '/app/task/queryTask?' + qs.stringify(data) })
}

/**
 * 审核详情
 * @param params url请求参数
 * https://yapi.ops.yunlizhi.cn/project/1018/interface/api/123526
 * @returns
 */
export function auditDetail(params?: any) {
  return request.get({
    url: `/api/app/audit/auditDetail?${qs.stringify(params)}`
  })
}

// 批量查询任务
export function batchQueryTask(data: any) {
  return request.post({ url: '/app/task/batchQueryTask', data })
}
