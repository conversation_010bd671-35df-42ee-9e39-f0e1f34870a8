import request from '@/utils/request'

/**
 * @description 获取文章分类
 * @return { Promise }
 */
export function getArticleCate() {
    return request.get({ url: '/app/appArticleCategory/list' })
}

/**
 * @description 获取文章列表
 * @return { Promise }
 */
export function getArticleList(data: Record<string, any>) {
    return request.get({ url: '/app/appArticle/page', data: data })
}

/**
 * @description 获取文章详情
 * @return { Promise }
 * @param data
 */
export function getArticleDetail(data: { id: string; userId: string }) {
    return request.get({
        url:
            data.userId === undefined
                ? `/app/appArticle/details/${data.id}/1`
                : `/app/appArticle/details/${data.id}/${data.userId}`
    })
}

/**
 * @description 加入收藏
 * @return { Promise }
 * @param data
 */
export function addCollect(data: { articleId: string }) {
    return request.post({ url: '/app/collect', data: data })
}

/**
 * @description 取消收藏
 * @return { Promise }
 * @param data
 */
export function cancelCollectByArticleId(data: { articleId: string }) {
    return request.delete({ url: '/app/collect/' + data.articleId })
}

/**
 * @description 取消收藏
 * @return { Promise }
 * @param data
 */
export function cancelCollect(data: { id: string }) {
    return request.delete({ url: '/app/collect', data: [data.id] })
}

/**
 * @description 获取收藏列表
 * @return { Promise }
 */
export function getCollect(pageNo: number, pageSize: number) {
    const data = { current: pageNo, size: pageSize }
    return request.get({ url: '/app/collect/page', data: data })
}
