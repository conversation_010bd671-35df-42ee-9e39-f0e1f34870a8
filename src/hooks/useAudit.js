import { completeTaskBatch, batchQueryTask, easyBatchCompleteTask } from '@/api/todo'
import { ref } from 'vue'
import { TodoBatchAuditResultEnum, TodoBatchAuditResultNumberEnum } from '@/enums/constant'
export function useAudit() {
  const batchQueryTaskData = ref({})
  const approveCondition = ref()
  const approveDesc = ref()
  function buildParams() {
    const params = {
      completeTaskList: []
    }
    Object.keys(batchQueryTaskData.value).forEach((key) => {
      if (batchQueryTaskData.value[key]) {
        let process = {}
        const paramMap = {}
        const formItems = batchQueryTaskData.value[key]?.formItems || []
        if (formItems?.length) {
          formItems.forEach((item) => {
            paramMap[item.id] = item?.props?.value || null
          })
        }
        try {
          process = JSON.parse(batchQueryTaskData.value[key].process)
          // eslint-disable-next-line no-empty
        } catch (error) {}
        const childNodeIds = process?.childNode?.id
        if (childNodeIds) {
          paramMap[`${childNodeIds}_approve_condition`] = TodoBatchAuditResultEnum[approveCondition.value]
        }
        params.completeTaskList.push({
          taskId: key,
          processInstanceId: batchQueryTaskData.value[key].processInstanceId,
          paramMap,
          taskLocalParamMap: {
            approveDesc: approveDesc.value,
            approvalStatus: TodoBatchAuditResultNumberEnum[approveCondition.value]
          }
        })
      }
    })
    return params
  }

  async function getBatchQueryTask(taskIdList = []) {
    const res = await batchQueryTask({
      taskIdList,
      view: false
    })
    batchQueryTaskData.value = res?.data || {}
  }

  async function submitHard(records = [], formData = {}) {
    approveCondition.value = formData?.approveCondition
    approveDesc.value = formData?.approveDesc || null
    await getBatchQueryTask(records)
    const params = buildParams()
    await completeTaskBatch(params)
  }

  async function submit(records = [], formData = {}) {
    // approveCondition.value = formData?.approveCondition
    // approveDesc.value = formData?.approveDesc || null
    // await getBatchQueryTask(records)
    // const params = buildParams()
    const params = {
      taskIdList: records,
      approveDesc: formData?.approveDesc || null,
      approvalStatus: formData?.approveCondition
    }
    await easyBatchCompleteTask(params)
  }

  return {
    submit,
    submitHard
  }
}
