import { ref, isRef, onMounted, computed } from 'vue'
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
export function getRef(value) {
  return isRef(value) ? value : ref(value)
}
export function useProList(options) {
  const {
    apiFn, // 接口对象
    extParams = {}, // 固定的body入参
    extQuerys = {}, // 固定的url入参
    isClearReqEmptyField = true, // 提交给接口是否删除无效字段
    paramsHandler, // 最终提交给接口的参数劫持方法，用户修改最终提交的参数
    querysHandler, // 最终提交给接口的url参数劫持方法，用户修改最终提交的参数
    responseHandler, // 最终入参给表格里面的数据，用于格式化接口返回不符合前端表格规范
    lazyLoad = false,
    addPageEvent = true
  } = options
  const list = ref([])
  const apiFnRef = getRef(apiFn)
  const extParamsRef = getRef(extParams)
  const extQuerysRef = getRef(extQuerys)
  const loadedOnce = ref(false) // 是否至少加载过一次
  const finished = ref(false) // 是否全部加载完毕
  const pagination = ref({
    loading: false,
    page: 1,
    size: 20,
    total: null
  })
  async function remoteMethod() {
    const { page, size, loading } = pagination.value
    if (finished.value || loading) {
      return
    }
    const query = {
      pageSize: size,
      pageNum: page
    }
    try {
      pagination.value.loading = true
      const res = await apiFnRef.value({ ...extParamsRef.value, ...query }, { ...extQuerysRef.value, ...query })
      pagination.value.loading = false
      const records = res?.data?.list || res?.data?.records
      pagination.value.total = res?.data?.total
      pagination.value.page++
      const newList = responseHandler ? responseHandler(records) : records
      const listResult = [...list.value, ...newList]
      if (listResult.length === pagination.value.total || pagination.value.total === 0) {
        finished.value = true
      }
      list.value = listResult
      loadedOnce.value = true
    } catch (e) {
      pagination.value.loading = false
      loadedOnce.value = false
    }
  }

  function scrollToTop() {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 300
    })
  }

  async function refresh() {
    if (pagination.value.loading) {
      return
    }
    finished.value = false
    pagination.value = {
      loading: false,
      page: 1,
      size: 20,
      total: null
    }
    list.value = []
    loadedOnce.value = false
    uni.stopPullDownRefresh()
    await remoteMethod()
    scrollToTop()
  }

  const isEmpty = computed(() => {
    return list.value.length === 0 && loadedOnce.value
  })
  onMounted(() => {
    if (!lazyLoad) {
      remoteMethod()
    }
  })
  onPullDownRefresh(() => {
    if (!addPageEvent) {
      return
    }
    refresh()
  })
  onReachBottom(() => {
    if (!addPageEvent) {
      return
    }
    remoteMethod()
  })

  return {
    list,
    isEmpty,
    refresh,
    finished,
    scrollToTop
  }
}
