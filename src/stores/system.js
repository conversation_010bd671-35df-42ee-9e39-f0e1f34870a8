import { defineStore } from 'pinia'

export const useSystemStore = defineStore('system', {
    state: () => {
        return {
            systemInfo: {}, // 手机系统信息
            navBarHeight: 44, // 系统默认导航栏高度
            statusBarHeight: 0, // 系统默认状态栏高度
            safeAreaBottom: 0, // 底部安全区域距离
            windowWidth: 320, // 手机屏幕宽度
            windowHeight: 568, // 手机屏幕高度
            menuButtonInfo: {
                top: 0,
                height: 0
            }, // 胶囊按钮
            titleBarHeight: 0
        }
    },
    getters: {
        navBarAndStatusBarHeight() {
            return `${+(this.statusBarHeight || 0) + (this.navBarHeight || 0)}px`
        }
    },
    actions: {
        initSystemInfo() {
            const systemInfo = wx.getSystemInfoSync()
            console.log('系统信息', systemInfo)
            const navBarHeight = `${systemInfo.platform == 'ios' ? 44 : 48}`
            const statusBarHeight = `${systemInfo.statusBarHeight || 0}`
            const safeAreaBottom = systemInfo.screenHeight - systemInfo?.safeArea?.bottom || 0
            const res = uni.getMenuButtonBoundingClientRect()
            this.menuButtonInfo = res
            this.titleBarHeight = res?.top + (res?.height - statusBarHeight) / 2

            // 存储系统信息
            this.systemInfo = systemInfo
            // 存储导航栏的高度
            this.navBarHeight = navBarHeight
            // 存储状态栏的高度
            this.statusBarHeight = statusBarHeight
            // 存储手机底部安全区域距离
            this.safeAreaBottom = safeAreaBottom
            // 存储手机屏幕宽度
            this.windowWidth = systemInfo.windowWidth
            // 存储手机屏幕高度
            this.windowHeight = systemInfo.windowHeight
        }
    },
    persist: {
        storage: {
            getItem: uni.getStorageSync,
            setItem: uni.setStorageSync
        }
    }
})
