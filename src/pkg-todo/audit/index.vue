<template>
  <page-meta
    class="pkg-todo-audit-page-meta"
    background-color="#F2F3F5"
    page-style="overflow-hidden"
  >
    <view :class="['pages-container', { 'is-view': isView }]">
      <view class="floatheader">
        <view
          class="todo-wrapper--space"
          :style="{ height: '24px' }"
        ></view>
        <view class="todo-wrapper--header">
          <image
            class="todo-wrapper--header__left"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/order/caret-left.png"
            mode="aspectFit"
            @tap.stop="handleBack"
          />
          <view class="todo-wrapper--header__title">{{ pageTitle }}</view>
          <view class="todo-wrapper--header__right"></view>
        </view>
      </view>
      <view :class="['audit-wrapper', { detail: TodoOperationTypeEnum.AUDIT_DETAIL === operationType }]">
        <Card>
          <view class="audit-container">
            <view class="audit-container-header">
              <text class="audit-container-header-text">{{ taskItem.processName || '--' }}</text>
              <view class="audit-container-header-tag">{{ auditResult.title }}</view>
            </view>
            <view class="audit-container-content">
              <view class="audit-container-content-item">
                <view class="audit-container-content-item-label">申请人员</view>
                <view class="audit-container-content-item-value">
                  {{ taskItem.rootUserName || '--' }}
                </view>
              </view>
              <view class="audit-container-content-item">
                <view class="audit-container-content-item-label">申请时间</view>
                <view class="audit-container-content-item-value">
                  {{ taskItem.startTime ? formatDate(taskItem.startTime, 'YYYY-MM-DD HH:mm') : '--' }}
                </view>
              </view>
            </view>
          </view>
        </Card>
        <Card title="审批详情">
          <!-- <view class="">~暂无审批详情数据~</view> -->
          <!-- 积分账户 -->
          <Detail
            v-if="ASSOCIATION_TYPE_ENUM.BizIntegralAccountEntity === associationType"
            :options="BIZ_INTEGRAL_ACCOUNT_ENTITY_COLUMNS"
            :info="info"
          />

          <!-- 积分 -->
          <Detail
            v-if="ASSOCIATION_TYPE_ENUM.BizIntegralDetailEntity === associationType"
            :options="BIZ_INTEGRAL_DETAIL_ENTITY_COLUMNS"
            :info="info"
          />

          <!-- 订单 -->
          <Detail
            v-if="ASSOCIATION_TYPE_ENUM.BizOrderInfoEntity === associationType"
            :options="BIZ_ORDER_INFO_ENTITY_COLUMNS_COMPUTED"
            :info="info"
          />

          <!-- 金币 -->
          <Detail
            v-if="ASSOCIATION_TYPE_ENUM.BizGoldCoinDetail === associationType"
            :options="BIZ_GOLD_COIN_DETAIL_COLUMNS"
            :info="info"
          />

          <!-- 卡券 -->
          <Detail
            v-if="ASSOCIATION_TYPE_ENUM.BizCouponInstanceEntity === associationType"
            :options="BIZ_COUPON_INSTANCE_ENTITY_COLUMNS"
            :info="info"
          />
        </Card>
        <Card title="审批记录">
          <Step :tableData="auditStep" />
        </Card>
        <Card
          title="审批意见"
          v-if="TodoOperationTypeEnum.AUDIT === operationType"
        >
          <view class="audit-wrapper--textarea">
            <uv-input
              type="textarea"
              v-model="auditForm.approveDesc"
              placeholder="请输入您的审批意见"
              trim
              border="none"
              bgColor="#f6f7f9"
              placeholderStyle="color: #7E8694;font-size: 28rpx;"
              :customStyle="{ backgroundColor: '#f6f7f9' }"
            ></uv-input>
          </view>
        </Card>
      </view>

      <pro-bottom-button
        v-if="TodoOperationTypeEnum.AUDIT === operationType"
        :config="defaultConfigs"
        @onConfirm="submit({ approveCondition: TodoBatchAuditResultEnum.APPROVED })"
        @onCancel="submit({ approveCondition: TodoBatchAuditResultEnum.REJECTED })"
      />
    </view>
  </page-meta>
</template>

<script setup lang="js">
import { ref, computed } from 'vue'
import Card from '../components/card/index.vue'
import { TodoBatchAuditResultEnum, TodoOperationTypeEnum } from '@/enums/constant';
import { auditDetail, formatStartNodeShow } from '@/api/todo';
import { onLoad } from '@dcloudio/uni-app';
import { bizCustomFields } from '@/api/ocr'
import { getSecret, getToken, logout } from '@/api/user'
import {
    ASSOCIATION_TYPE_ENUM,
    ASSOCIATION_TYPE_ENUM_FIELD,
    BIZ_INTEGRAL_ACCOUNT_ENTITY_COLUMNS,
    BIZ_INTEGRAL_DETAIL_ENTITY_COLUMNS,
    BIZ_ORDER_INFO_ENTITY_COLUMNS,
    BIZ_COUPON_INSTANCE_ENTITY_COLUMNS,
    BIZ_GOLD_COIN_DETAIL_COLUMNS,
    // mock_data
} from './constant'
import Detail from '../components/detail.vue';
import Step from '../components/step/index.vue'
import { useAudit } from '@/hooks/useAudit'
import { formatDate } from '@/utils/dateFormat'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const pageTitle = ref('待审批详情')
const {submit: submitAudit} = useAudit()
const operationType = ref()
const loading = ref(false);
const taskItem = ref({})
const submitLoading = ref(false);
const info = ref({
    detailList: [],
});
const associationType = ref({});
const auditForm = ref({
    approveDesc: null,
    approveCondition: TodoBatchAuditResultEnum.APPROVED,
})
const auditStep = ref([]);

const bizCustomFieldsList = ref([])

const isView = computed(() => {
    return !!(TodoOperationTypeEnum.AUDIT_DETAIL === operationType.value)
})
async function getBizCustomFields() {
    const businessType = info.value?.businessType || ''
    if (!businessType) {
        return
    }
    const res = await bizCustomFields({
        businessType
    })
    bizCustomFieldsList.value = (res.data || [])?.map((item) => {
        return {
            ...item,
            mapObj: {
                ...item?.ext?.dictItems?.reduce((acc, itemOption) => {
                    acc[itemOption.itemCode] = itemOption.label
                    return acc
                }, {})
            },
        }
    })
}


const BIZ_ORDER_INFO_ENTITY_COLUMNS_COMPUTED = computed(() => {
    const findIndexOfDynamicForm = BIZ_ORDER_INFO_ENTITY_COLUMNS.findIndex(item => item.prop === 'recipientPhone');
    const preColumns = BIZ_ORDER_INFO_ENTITY_COLUMNS.slice(0, findIndexOfDynamicForm + 1)
    const findIndexOfProduct = BIZ_ORDER_INFO_ENTITY_COLUMNS.findIndex(item => item.prop === 'otherAttachmentUrl')
    const postColumns = BIZ_ORDER_INFO_ENTITY_COLUMNS.slice(findIndexOfProduct)

    // 插入产品
    const detailList = info.value?.detailList || [];
    const dynamicFormColumns = detailList.map((item, index) => {
        return {
            prop: 'detailList',
            label: item.productName,
            group: '发放产品',
            sort: index * 10,
            formatter: () => `${item?.issueQuantity || 0}（面值: ${item?.faceValue || 0}）`
        }
    })

    // 插入动态字段
    // const recipientExt = info.value?.recipientExt || {};
    // const recipientExtColumns = Object.keys(recipientExt)?.map((item, index) => {
    //     const obj = bizCustomFieldsList.value?.find(itemFiled => itemFiled?.fieldCode === item);
    //     return {
    //         prop: 'recipientExt',
    //         label: obj?.customFieldName || obj?.defaultFieldName || null,
    //         group: '发放对象信息',
    //         sort: index * 10,
    //         formatter: () => `${ obj?.mapObj?.[recipientExt[item]] || recipientExt[item] || ''}`
    //     }
    // })?.filter(item => !!item.label)
    const recipientExt = info.value?.recipientExt || {};
    const recipientExtColumns = bizCustomFieldsList.value?.map((item, index) => {
        const key = item?.fieldCode
        return {
            key,
            prop: 'recipientExt',
            label: item?.customFieldName || item?.defaultFieldName || null,
            group: '发放对象信息',
            sort: index * 10,
            formatter: () => `${ item?.mapObj?.[recipientExt[key]] || recipientExt[key] || '--'}`
        }
    })?.filter(item => !!item.label && !['policyAttachmentUrl', 'otherAttachmentUrl'].includes(item.key))

    return [...preColumns, ...dynamicFormColumns, ...recipientExtColumns, ...postColumns].filter((item) => {
        const files = ['policyAttachmentUrl', 'otherAttachmentUrl'];
        if(files.includes(item.prop)) {
            return bizCustomFieldsList.value.some((itemFiled) => files.includes(itemFiled?.fieldCode))
        }
        return true
    })
})

const auditResult = computed(() => {
    const approvalStatus = String(taskItem.value?.approvalStatus);
    const processStatus = String(taskItem.value?.processStatus);
    const lastComment = taskItem.value?.lastComment;
    if (approvalStatus === '1' && processStatus === '2') {
        return {
            title: '审批通过',
            icon: 'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/audit-success.png',
        }
    } else if (approvalStatus === '2' && processStatus === '2') {
        return {
            title: '审批不通过',
            lastComment,
            icon: 'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/audit-failed.png',
        }
    } else {
        return {
            title: '待审批',
            lastComment,
            icon: 'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/auditing.png',
        }
    }
})



const defaultConfigs = computed(() => {
  return {
    cancelBtnText: "审批不通过",
    confirmBtnText: "审批通过",
    showCancelBtn: true,
    showConfirmBtn: true,
    confirmDisabled: submitLoading.value,
    cancelDisabled: submitLoading.value,
  };
});

async function submit(extra = {}) {
    if (submitLoading.value) {
        return
    }
    submitLoading.value = true;
    // const params = {
    //     ...auditForm.value,
    //     ...extra,
    //     taskList: [{
    //         taskId: taskItem.value.taskId,
    //         nodeId: taskItem.value.nodeId,
    //         processInstanceId: taskItem.value.processInstanceId,
    //     }]
    // }
    try {
        // await completeTaskBatch(params)
        await submitAudit([taskItem.value.taskId], { ...auditForm.value, ...extra })
        uni.showToast({
            title: '操作成功',
            icon: 'success',
        })

        setTimeout(() => {
            uni.reLaunch({
                url: '/pages/todo/todo'
            })
        }, 1000)
    // eslint-disable-next-line no-empty
    } catch (error) {
        uni.showToast({
            title: error?.message || error?.msg || '提交失败',
            icon: 'none',
        })
    }
    submitLoading.value = false;
}


/**
 * 递归处理树形结构数据的方法
 * @param {Array} items - 需要处理的原始数据数组
 * @returns {Array} - 处理后的树形结构数据数组
 */
 const processTreeData = (items) => {
  // 遍历原始数据数组中的每个元素
  return items.map((item, index) => {
    // 复制当前元素，避免修改原始数据
    const processedItem = { ...item };
    // 为每个元素添加唯一标识
    processedItem.mid = item.id;
    processedItem.name = item.name;
    if (itme.type == 4) {
        processedItem.name = '审批';
    } else if (item.type == 5) {
        processedItem.name = '并行审批';
    }

    // 系统起始点
    if (index === 0 && item.id === 'root') {
        processedItem.isStart = true;
    }
    // 系统结束点
    if (index === items.length - 1 && item.id === 'root') {
        processedItem.isEnd = true;
    }
    // 初始化子节点数组
    processedItem.chNode = [];
    // 处理 userVoList 字段，如果 userVoList 存在且元素数量大于 1
    if (item.userVoList && item.userVoList.length > 1) {
      // 将 userVoList 中的每个元素转换为一个子节点对象
      processedItem.chNode = item.userVoList.map((item1, ind) => ({
        mid: `${item.id}a${ind}`, // 生成子节点的唯一标识
        userVoList: [{ name: item1.name, approveDesc: item1.approveDesc ,username:item1.username }], // 存储审批人信息
        approvalStatus: item1.approvalStatus, // 存储审批状态
        chNode: [] // 初始化子节点的子节点数组
      }));
    }
    if(item.userGroupList&&item.userGroupList.length>0){
      processedItem.chNode = []
      processedItem.userVoList = []
       item.userGroupList.forEach((depOrRole, ind) => {
        depOrRole.userVoList.forEach((item1, ind) => {
          processedItem.chNode.push({
          mid: `${item.id}b${ind}`, // 生成子节点的唯一标识
          userVoList: [{ name: item1.name, approveDesc: item1.approveDesc,username:item1.username}], // 存储审批人信息
          approvalStatus: item1.approvalStatus, // 存储审批状态
          extra:depOrRole.name
        })
        processedItem.userVoList.push({ name: item1.name, approveDesc: item1.approveDesc ,username:item1.username})
        })
      })
    }

    // 处理 branch 字段，如果 branch 存在且元素数量大于 0
    if (item.branch && item.branch.length > 0) {
      // 初始化过滤后的 branch 数组
      let filteredBranch = item.branch;
      // 如果当前元素的 type 为 4，对 branch 数组进行过滤
      if (item.type === 4) {
        filteredBranch = item.branch.filter((itembrach) => itembrach.isApprovalRoute);
        console.log(filteredBranch, "filteredBranch");
        if(filteredBranch.length===0){
          // filteredBranch = [{}]
        }
      }

      // 将过滤后的 branch 数组转换为子节点对象，并添加到当前元素的 chNode 数组中
      processedItem.chNode = [
        ...processedItem.chNode,
        ...filteredBranch.map((bra, indexbra) => {
          // 初始化子节点对象
          const chRow = {
            mid: `${item.id}b${indexbra}`, // 生成子节点的唯一标识
            userVoList: [], // 初始化审批人信息数组
            approvalStatus: bra?.approvalStatus, // 存储审批状态
            chNode: [], // 初始化子节点的子节点数组
            isbranch: true, // 标记当前节点为分支节点
            name:bra.placeholder// 存储节点名称，默认为 '分支
          };

          // 如果当前 branch 元素的 children 存在且元素数量大于 1
          if (bra.children && bra.children.length > 0) {
            // 递归处理 children 数组
            chRow.chNode = processTreeData(bra.children);
            // 将 children 数组中的每个元素转换为审批人信息对象
            // chRow.userVoList = bra.children.map((chi) => ({
            //   name: chi.name,
            //   approveDesc: chi.approveDesc
            // }));
            chRow.userVoList = []
          }
          //  else if (bra.children && bra.children.length === 1) {
          //   // 如果当前 branch 元素的 children 只有一个元素，直接添加到审批人信息数组中
          //   chRow.userVoList.push({
          //     name: bra.children[0].name,
          //     approveDesc: bra.children[0].approveDesc
          //   });
          // }

          return chRow;
        })
      ];
    }

    return processedItem;
  });
};

async function loadAuditRecord() {
    auditStep.value = processTreeData([]);
    loading.value = true;
    try {
        const res = await formatStartNodeShow({
            flowId: taskItem.value?.flowId,
            paramMap: {},
            processInstanceId: taskItem.value?.processInstanceId,
            taskId: '',
        });
        const data = processTreeData(res.data || []);
        const currentNodeIndex = data.findIndex(item => item.approvalStatus === 3);
        const currentNodePassIndex = data.findIndex(item => item.approvalStatus === 4);
        auditStep.value = data?.map((item, index )=> {
            return {
                ...item,
                // 标识时间轴状态
                hasDone: !!(index <= currentNodeIndex) || item.approvalStatus === 4 || !!(index <= currentNodePassIndex),
                hasError: [6, 7].includes(item.approvalStatus),
            }
        });
        console.log(auditStep.value, 'loadAuditRecord');
    } catch (error) {
        console.log('error', error)
    }
    loading.value = false;
}

async function auditDetailFn() {
    loading.value = true;
    try {
        const res = await auditDetail({
            processInstanceId: taskItem.value.processInstanceId,
        });
        associationType.value = res?.data?.associationType || ''
        info.value = res?.data[ASSOCIATION_TYPE_ENUM_FIELD[res?.data?.associationType]] || {}
    } catch (error) {
        console.log('error', error)
        info.value = {}
    }
    loading.value = false;
}

function setNavigationBarTitle() {
    if (operationType.value === TodoOperationTypeEnum.AUDIT) {
        pageTitle.value = '待审批详情'
        uni.setNavigationBarTitle({
            title: '待审批详情',
            backgroundColor: '#F3F4F6'
        })
    } else if (operationType.value === TodoOperationTypeEnum.AUDIT_DETAIL) {
        pageTitle.value = '审批详情'
        uni.setNavigationBarTitle({
            title: '审批详情',
            backgroundColor: '#F3F4F6'
        })
    }
}

function handleBack() {
    uni.reLaunch({
        url: '/pages/todo/todo'
    })
}

async function changeTenant(params, cb) {
    const { username, tenantId } = params
    const { data: secret } = await getSecret({ tenantId, username })
        await logout()
        // 顺序不能改
        const tokenData = await getToken({
            username,
            secret,
            grant_type: 'username',
            scope: 'server'
        })
        setTimeout(() => {
            userStore.login(tokenData.access_token)
            userStore.getUser()
            cb()
        }, 200)
}
async function initData() {
    setNavigationBarTitle()
    loadAuditRecord()
    await auditDetailFn()
    await getBizCustomFields()
}
onLoad(async (options) => {
    const res = JSON.parse(options?.taskItem || '{}')
    // const res222 = {
    //     approvalStatus: null,
    //     flowId: "P20250527151831935NEDEB",
    //     lastComment: null,
    //     nodeId: "mb66gfdiqy7o7",
    //     processInstanceId: "4a56254e-3ad5-11f0-bb68-1ea949ea0b66",
    //     processName: "0527回归分支1",
    //     processStatus: 1,
    //     rootUserName: "管理员",
    //     startTime: "2025-05-27 16:33:40",
    //     taskId: "5f725a41-3ad5-11f0-bb68-1ea949ea0b66"
    // }
    // const res = {
    //     approvalStatus: null,
    //     flowId: "P20250527174150685P9FXI",
    //     lastComment: null,
    //     nodeId: "mb67xa8blh0uj",
    //     processInstanceId: "4e946958-3aeb-11f0-b322-da95179400f3",
    //     processName: "并行或签会签",
    //     processStatus: 1,
    //     rootUserName: "管理员",
    //     startTime: "2025-05-27 19:11:16",
    //     taskId: "4f872e06-3aeb-11f0-b322-da95179400f3",
    // }
    operationType.value = options?.operationType || '';
    taskItem.value = res;

    if(options?.type === 'message') {
        changeTenant(options, initData)
    } else {
        initData();
    }
})
</script>
<style scoped lang="scss">
.pages-container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background: #f2f3f5;
  height: 100vh;
  overflow: hidden;
  background: url('https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/bg-todo-new.png')
    no-repeat 0 0;
  background-size: 100% auto;
  // background-color: linear-gradient(180deg, #ebf8ff 64.85%, #f2f3f5 89.6%);
  .floatheader {
    position: fixed;
    z-index: 20;
    background-color: transparent;
    width: 100%;
    left: 0;

    .todo-wrapper--header {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 0 12px;
      height: 64rpx;
      &__left,
      &__right {
        width: 48rpx;
        height: 48rpx;
        flex-shrink: 0;
      }
      &__title {
        font-weight: 500;
        color: #1c2026;
        flex: 1;
        text-align: center;

        color: var(--text-icon-font-gy-190, rgba(0, 0, 0, 0.9));
        /* Title/Large */
        font-family: 'PingFang SC';
        font-size: 36rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
      }
    }
  }

  .audit-container {
    &-content {
      &-item {
        height: 44rpx;
        margin-bottom: 8rpx;
        display: flex;
        align-items: center;
        gap: 16rpx;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        &:last-child {
          margin-bottom: 0;
        }
        &-label {
          color: var(--Neutral-secondary, #7e8694);
          width: 140rpx;
          flex-shrink: 0;
        }
        &-value {
          color: var(--Neutral-primary, #1c2026);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    &-header {
      display: flex;
      align-items: center;
      height: 56rpx;
      gap: 16rpx;
      margin-bottom: 16rpx;
      &-text {
        color: var(--Neutral-primary, #1c2026);
        /* Semibold/T2-20 */
        font-family: 'PingFang SC';
        font-size: 40rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 28px; /* 140% */
      }
      &-tag {
        border-radius: 4px;
        background: var(--background-warning, #fff7e8);
        padding: 0 12rpx;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: max-content;
        color: var(--text-press-warning, #d25f00);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        &.success {
          background: var(--background-success, #e8ffea);
          color: var(--text-press-success, #00b42a);
        }
        &.error {
          background: var(--background-error, #ffece8);
          color: var(--text-press-error, #f53f3f);
        }
        &.warning {
          background: var(--background-warning, #fff7e8);
          color: var(--text-press-warning, #d25f00);
        }
      }
    }
  }

  .audit-wrapper {
    padding: 24rpx 32rpx;
    padding-bottom: 220rpx;
    margin-top: 170rpx;
    box-sizing: border-box;
    flex: 1;
    overflow-y: auto;
    &.is-view {
      padding-bottom: 32px;
    }
    &.detail {
      padding-bottom: 48rpx;
    }
    &--content {
      margin-top: 16rpx;

      &--item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 44rpx;
        margin-top: 4rpx;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        &--label {
          color: var(--Neutral-secondary, #7e8694);
          width: 156rpx;
          flex-shrink: 0;
        }

        &--value {
          color: var(--Neutral-primary, #1c2026);
          flex-shrink: 0;
          flex: 1;
        }
      }
    }
    &--textarea {
      padding: 24rpx;
      background-color: #f6f7f9;
      border-radius: 12rpx;
      margin-top: 16rpx;
      :deep(.uv-input__content-textarea) {
        // background-color: #f6f7f9;
      }
    }

    &--status {
      padding: 0rpx 16rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 32rpx;
      box-sizing: border-box;
      margin-bottom: 32rpx;

      &--icon {
        width: 96rpx;
        height: 88rpx;
        flex-shrink: 0;
      }

      &--item {
        flex: 1;
        font-style: normal;
        font-family: 'PingFang SC';
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;

        &--label {
          color: var(--Neutral-primary, #1c2026);
          font-size: 32rpx;
          font-weight: 600;
          height: 48rpx;
        }

        &--desc {
          color: var(--Neutral-regular, #505762);
          font-size: 24rpx;
          font-weight: 400;
          height: 40rpx;
          margin-top: 4rpx;
        }
      }
    }
  }
}
</style>
