// 预置 5 种深色渐变色
export const gradientColors = [
  'linear-gradient(180deg, #B751FF 0%, #8F25DB 100%)',
  'linear-gradient(180deg, #5F8FFF 0%, #3069F0 100%)',
  'linear-gradient(135deg, #F8000D 5.17%, #FF93CE 88.58%)',
  'linear-gradient(180deg, #FF363A 0%, #FF9532 100%)',
  'linear-gradient(180deg, #5F8FFF 0%, #1DBA83 100%)',
  'linear-gradient(180deg, #008B1F 0%, #99C200 100%)'
]
// 生成渐变色，循环使用预置颜色
export const getGradientColor = (index) => {
  // 通过取模运算循环使用预置的渐变色
  return gradientColors[index % gradientColors.length]
}
// 获取姓名最后两个字
export const getLastName = (name) => {
  return name.slice(-2)
}

// 状态
export const STATUS_ENUM = {
  1: '已提交',
  2: '未开始',
  3: '审批中',
  4: '已通过',
  5: '自动通过',
  6: '已拒绝',
  7: '自动拒绝',
  8: '已结束',
  9: '已抄送',
  10: '未结束',
  11: '转办'
}

//导出类型枚举
export const ASSOCIATION_TYPE_ENUM = {
  BizCouponInstanceEntity: 'BizCouponInstanceEntity', // 卡券
  BizGoldCoinDetail: 'BizGoldCoinDetail', // 金币
  BizIntegralDetailEntity: 'BizIntegralDetailEntity', // 积分
  BizOrderInfoEntity: 'BizOrderInfoEntity', // 订单
  BizIntegralAccountEntity: 'BizIntegralAccountEntity' // 积分账户
}
//导出类型枚举-名称
export const ASSOCIATION_TYPE_ENUM_NAME = {
  [ASSOCIATION_TYPE_ENUM.BizCouponInstanceEntity]: '卡券', // 卡券
  [ASSOCIATION_TYPE_ENUM.BizGoldCoinDetail]: '金币', // 金币
  [ASSOCIATION_TYPE_ENUM.BizIntegralDetailEntity]: '积分', // 积分
  [ASSOCIATION_TYPE_ENUM.BizOrderInfoEntity]: '订单', // 订单
  [ASSOCIATION_TYPE_ENUM.BizIntegralAccountEntity]: '积分账户' // 积分账户
}
//导出类型枚举-所对应的字段映射
export const ASSOCIATION_TYPE_ENUM_FIELD = {
  [ASSOCIATION_TYPE_ENUM.BizCouponInstanceEntity]: 'couponAuditDetailsVo', // 卡券
  [ASSOCIATION_TYPE_ENUM.BizGoldCoinDetail]: 'goldCoinDeductionReq', // 金币
  [ASSOCIATION_TYPE_ENUM.BizIntegralDetailEntity]: 'integralChangeVo', // 积分
  [ASSOCIATION_TYPE_ENUM.BizOrderInfoEntity]: 'bizOrderInfoVo', // 订单
  [ASSOCIATION_TYPE_ENUM.BizIntegralAccountEntity]: 'integralAccountInfoVo' // 积分账户
}

//导出操作枚举
export const OPERATION_TYPE_ENUM = {
  EXTENSION: 'EXTENSION', // 延期
  CANCEL: 'CANCEL', // 作废
  DEDUCTION: 'DEDUCTION', // 扣减
  RECHARGE: 'RECHARGE', // 充值
  FREEZE: 'FREEZE', // 冻结
  UNFREEZE: 'UNFREEZE', // 解冻
  WITHDRAW: 'WITHDRAW', // 撤回
  RECEIVED: 'RECEIVED' // 发放
}
//导出操作枚举-名称
export const OPERATION_TYPE_ENUM_NAME = {
  [OPERATION_TYPE_ENUM.EXTENSION]: '延期', // 延期
  [OPERATION_TYPE_ENUM.CANCEL]: '作废', // 作废
  [OPERATION_TYPE_ENUM.DEDUCTION]: '扣减', // 扣减
  [OPERATION_TYPE_ENUM.RECHARGE]: '充值', // 充值
  [OPERATION_TYPE_ENUM.RECEIVED]: '发放', // 充值
  [OPERATION_TYPE_ENUM.FREEZE]: '冻结', // 冻结
  [OPERATION_TYPE_ENUM.UNFREEZE]: '解冻', // 解冻
  [OPERATION_TYPE_ENUM.WITHDRAW]: '撤回' // 撤回
}

// 导出积分的操作类型
export const INTEGRAL_OPERATE_TYPE_ENUM = {
  WITHDRAW: '积分撤回',
  RECHARGE: '积分充值',
  RECEIVED: '积分发放',
  FREEZE: '积分冻结',
  UNFREEZE: '积分解冻',
  EXTENSION: '积分延期',
  CANCEL: '积分作废'
}

// 导出金币的操作类型
export const GOLD_COIN_OPERATE_TYPE_ENUM = {
  DEDUCTION: '驾享币扣减',
  CANCEL: '驾享币作废'
}

// 导出卡券的操作类型
export const COUPON_OPERATE_TYPE_ENUM = {
  EXTENSION: '卡券延期',
  CANCEL: '卡券作废'
}

// 导出配置字段信息列表

// 积分账户配置配置列
export const BIZ_INTEGRAL_ACCOUNT_ENTITY_COLUMNS = [
  {
    prop: 'accountName',
    label: '商家名称',
    group: '账户信息',
    sort: 10
    // show: () => true,
    // attrs: {},
    // enums: ACCOUNT_TYPE,
    // value: 'MERCHANT',
  },
  {
    prop: 'merchantShortName',
    label: '商家简称',
    group: '账户信息',
    attrs: {},
    sort: 20
  },

  {
    prop: 'accountCode',
    label: '账户编码',
    group: '账户信息',
    sort: 30
  },
  {
    prop: 'accountTypeName',
    label: '账户类型',
    group: '账户信息',
    sort: 40
  },
  {
    prop: 'integralModeName',
    label: '积分授予模式',
    group: '账户信息',
    sort: 50
  },
  {
    prop: 'rechargeIntegralRatio',
    label: '充值积分转换比（%）',
    group: '账户信息',
    sort: 60
  },

  {
    prop: 'platformIntegralValidity',
    label: '积分发放后有效期',
    group: '商家积分发放限制',
    unit: '天'
  },
  {
    prop: 'singleCustomerLimit',
    label: '单客户单次发放积分上限',
    group: '商家积分发放限制',
    unit: '元'
  },
  {
    prop: 'singleCustomerMonthlyIntegralLimit',
    label: '单客户月度发放积分上限',
    group: '商家积分发放限制',
    unit: '元'
  },
  {
    prop: 'singleCustomerMonthlyCountLimit',
    label: '单客户月度发放积分笔数上限',
    group: '商家积分发放限制',
    unit: '元'
  },
  {
    prop: 'singleCustomerYearlyIntegralLimit',
    label: '单客户年度发放积分上限',
    group: '商家积分发放限制',
    unit: '次'
  },
  {
    prop: 'singleCustomerYearlyCountLimit',
    label: '单客户年度发放积分笔数上限',
    group: '商家积分发放限制',
    unit: '次'
  },

  {
    prop: 'userTransferMin',
    label: '用户单笔积分转让最低',
    group: '用户积分转让设置',
    unit: '元'
  },
  {
    prop: 'userTransferMax',
    label: '用户单笔积分转让最高',
    group: '用户积分转让设置',
    unit: '元'
  },
  {
    prop: 'userTransferRate',
    label: '用户积分转让费率(%)',
    group: '用户积分转让设置'
  },
  {
    prop: 'userMonthlyIntegralLimit',
    label: '单用户月度累计转让积分上限',
    group: '用户积分转让设置',
    unit: '元'
  },
  {
    prop: 'userYearlyIntegralLimit',
    label: '单个用户年度累计转让积分上限',
    group: '用户积分转让设置',
    unit: '元'
  },
  {
    prop: 'userMonthlyCountLimit',
    label: '单个用户月度累计转让次数上限',
    group: '用户积分转让设置',
    unit: '次'
  },
  {
    prop: 'userYearlyCountLimit',
    label: '单个用户年度累计转让次数上限',
    group: '用户积分转让设置',
    unit: '次'
  }
]

// 积分配置配置列
export const BIZ_INTEGRAL_DETAIL_ENTITY_COLUMNS = [
  {
    prop: 'accountName',
    label: '账户名称',
    group: '',
    sort: 10,
    show: (data) => {
      return [
        OPERATION_TYPE_ENUM.WITHDRAW,
        OPERATION_TYPE_ENUM.RECHARGE,
        OPERATION_TYPE_ENUM.FREEZE,
        OPERATION_TYPE_ENUM.UNFREEZE
      ].includes(data?.operateType)
    }
  },
  {
    prop: 'accountCode',
    label: '账户编码',
    group: '',
    attrs: {},
    sort: 20,
    show: (data) => {
      return [
        OPERATION_TYPE_ENUM.WITHDRAW,
        OPERATION_TYPE_ENUM.RECHARGE,
        OPERATION_TYPE_ENUM.FREEZE,
        OPERATION_TYPE_ENUM.UNFREEZE
      ].includes(data?.operateType)
    }
  },
  {
    prop: 'userName',
    label: '客户',
    group: '',
    sort: 10,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.RECEIVED, OPERATION_TYPE_ENUM.EXTENSION, OPERATION_TYPE_ENUM.CANCEL].includes(
        data?.operateType
      )
    }
  },
  {
    prop: 'userPhone',
    label: '客户手机号',
    group: '',
    attrs: {},
    sort: 20,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.RECEIVED, OPERATION_TYPE_ENUM.EXTENSION, OPERATION_TYPE_ENUM.CANCEL].includes(
        data?.operateType
      )
    }
  },
  {
    prop: 'merchantName',
    label: '归属商家',
    group: '',
    sort: 30,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.RECEIVED, OPERATION_TYPE_ENUM.EXTENSION, OPERATION_TYPE_ENUM.CANCEL].includes(
        data?.operateType
      )
    }
  },
  {
    prop: 'operateType',
    label: '操作类型',
    group: '',
    sort: 40,
    formatter: (data) => {
      return INTEGRAL_OPERATE_TYPE_ENUM[data?.operateType] || '--'
    }
  },
  {
    prop: 'expireIntegral',
    label: '冻结积分',
    group: '',
    sort: 45,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.UNFREEZE].includes(data?.operateType)
    }
  },
  {
    prop: 'actualAmount',
    label: '到账积分',
    group: '',
    sort: 49,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.RECHARGE].includes(data?.operateType)
    }
  },
  {
    prop: 'amount',
    label: '撤回积分',
    group: '',
    sort: 50,
    renderLabel: (data) => {
      if ([OPERATION_TYPE_ENUM.FREEZE].includes(data?.operateType)) {
        return '冻结积分'
      }
      if ([OPERATION_TYPE_ENUM.UNFREEZE].includes(data?.operateType)) {
        return '解冻积分'
      }
      if ([OPERATION_TYPE_ENUM.CANCEL].includes(data?.operateType)) {
        return '作废积分'
      }
      if ([OPERATION_TYPE_ENUM.WITHDRAW].includes(data?.operateType)) {
        return '撤回积分'
      }
      if ([OPERATION_TYPE_ENUM.RECHARGE].includes(data?.operateType)) {
        return '充值积分'
      }
      if ([OPERATION_TYPE_ENUM.EXTENSION].includes(data?.operateType)) {
        return '延期积分'
      }
      if ([OPERATION_TYPE_ENUM.RECEIVED].includes(data?.operateType)) {
        return '发放积分'
      }
      return '--'
    }
  },
  {
    prop: 'remark',
    label: '撤回原因',
    group: '',
    sort: 60,
    renderLabel: (data) => {
      if ([OPERATION_TYPE_ENUM.FREEZE].includes(data?.operateType)) {
        return '冻结说明'
      }
      if ([OPERATION_TYPE_ENUM.UNFREEZE].includes(data?.operateType)) {
        return '解冻说明'
      }
      if ([OPERATION_TYPE_ENUM.CANCEL].includes(data?.operateType)) {
        return '作废原因'
      }
      if ([OPERATION_TYPE_ENUM.WITHDRAW].includes(data?.operateType)) {
        return '撤回说明'
      }
      if ([OPERATION_TYPE_ENUM.RECHARGE].includes(data?.operateType)) {
        return '充值说明'
      }
      if ([OPERATION_TYPE_ENUM.EXTENSION].includes(data?.operateType)) {
        return '延期原因'
      }
      if ([OPERATION_TYPE_ENUM.RECEIVED].includes(data?.operateType)) {
        return '发放原因'
      }
      return '--'
    }
  },
  {
    prop: 'attachment',
    label: '附件',
    group: '',
    type: 'files',
    sort: 70,
    show: (data) => {
      return [
        OPERATION_TYPE_ENUM.WITHDRAW,
        OPERATION_TYPE_ENUM.RECHARGE,
        OPERATION_TYPE_ENUM.FREEZE,
        OPERATION_TYPE_ENUM.UNFREEZE
      ].includes(data?.operateType)
    }
  }
]

// 卡券配置配置列
export const BIZ_COUPON_INSTANCE_ENTITY_COLUMNS = [
  {
    prop: 'changeType',
    label: '操作类型',
    group: '',
    sort: 10,
    formatter: (data) => {
      return COUPON_OPERATE_TYPE_ENUM[data?.changeType] || '--'
    }
  },
  {
    prop: 'couponName',
    label: '卡券名称',
    group: '',
    sort: 20
  },
  {
    prop: 'faceValue',
    label: '卡券面值',
    group: '',
    sort: 30
  },
  {
    prop: 'expirationTime',
    label: '有效期至',
    group: '',
    sort: 40,
    renderLabel: (data) => {
      if ([OPERATION_TYPE_ENUM.CANCEL].includes(data?.changeType)) {
        return '有效期至'
      }
      if ([OPERATION_TYPE_ENUM.EXTENSION].includes(data?.changeType)) {
        return '有效期变更至'
      }
      return '--'
    }
  },
  {
    prop: 'hisExpirationTime',
    label: '原有效期至',
    group: '',
    sort: 40,
    show: (data) => {
      return [OPERATION_TYPE_ENUM.EXTENSION].includes(data?.changeType)
    }
  },
  {
    prop: 'couponType',
    label: '业务类型',
    group: '',
    sort: 50,
    formatter: (data) => {
      const configStore = {}
      const bizBusinessType = configStore?.bizBusinessTypeObject || {}
      return bizBusinessType[data?.couponType] || '--'
    }
  },
  {
    prop: 'sendMerchantName',
    label: '发放商家',
    group: '',
    sort: 60
  },
  {
    prop: 'createBy',
    label: '发放人员',
    group: '',
    sort: 70
  },
  {
    prop: 'createTime',
    label: '发放时间',
    group: '',
    sort: 80
  },
  {
    prop: 'useMerchantName',
    label: '使用门店',
    group: '',
    sort: 90
  },
  {
    prop: 'userName',
    label: 'C端客户',
    group: '',
    sort: 100
  },
  {
    prop: 'phone',
    label: '客户手机号',
    group: '',
    sort: 100
  },
  {
    prop: 'remark',
    label: '作废原因',
    group: '',
    sort: 110
  }
]

// 金币配置配置列
export const BIZ_GOLD_COIN_DETAIL_COLUMNS = [
  {
    prop: 'userName',
    label: '客户',
    group: '',
    sort: 10
  },
  {
    prop: 'userPhone',
    label: '客户手机号',
    group: '',
    attrs: {},
    sort: 20
  },
  {
    prop: 'merchantName',
    label: '归属商家',
    group: '',
    sort: 30
  },
  {
    prop: 'operationType',
    label: '操作类型',
    group: '',
    sort: 40,
    formatter: (data) => {
      return GOLD_COIN_OPERATE_TYPE_ENUM[data?.operationType] || '--'
    }
  },
  {
    prop: 'changeAmount',
    label: '撤回积分',
    group: '',
    sort: 50,
    renderLabel: (data) => {
      if ([OPERATION_TYPE_ENUM.DEDUCTION].includes(data?.operationType)) {
        return '驾享币扣减'
      }
      if ([OPERATION_TYPE_ENUM.CANCEL].includes(data?.operationType)) {
        return '作废驾享币'
      }
      return '--'
    }
  },
  {
    prop: 'changeDescription',
    label: '撤回原因',
    group: '',
    sort: 60,
    renderLabel: (data) => {
      if ([OPERATION_TYPE_ENUM.CANCEL].includes(data?.operationType)) {
        return '作废原因'
      }
      if ([OPERATION_TYPE_ENUM.DEDUCTION].includes(data?.operationType)) {
        return '扣减原因'
      }
      return '--'
    }
  }
]

// 订单配置配置列
export const BIZ_ORDER_INFO_ENTITY_COLUMNS = [
  {
    // prop: 'accountName',
    prop: 'applicant',
    label: '申请人',
    group: '基础信息',
    sort: 10
  },
  {
    prop: 'merchantName',
    label: '发放商家',
    group: '基础信息',
    sort: 20
  },
  {
    prop: 'businessType',
    label: '业务类型',
    group: '基础信息',
    sort: 30,
    formatter: (data) => {
      const configStore = {}
      const bizBusinessType = configStore?.bizBusinessTypeObject || {}
      return bizBusinessType[data?.businessType] || '--'
    }
  },
  {
    prop: 'policyAttachmentUrl',
    label: '保单附件',
    group: '发放对象信息',
    type: 'files',
    sort: 40
  },
  {
    prop: 'recipientName',
    label: '姓名',
    group: '发放对象信息',
    sort: 50
  },
  {
    prop: 'recipientPhone',
    label: '手机号码',
    group: '发放对象信息',
    sort: 60
  },
  // {
  //     prop: 'recipientExt',
  //     label: '',
  //     group: '发放对象信息',
  //     sort: 70
  // },
  // {
  //     prop: 'detailList',
  //     label: '',
  //     group: '发放产品',
  //     sort: 20
  // },
  {
    prop: 'otherAttachmentUrl',
    label: '其他附件',
    group: '其他信息',
    sort: 20,
    type: 'files'
  },
  {
    prop: 'remarks',
    label: '备注',
    group: '其他信息',
    sort: 20
  }
]

export const mock_data = {
  code: 0,
  bizCode: '0',
  msg: null,
  data: [
    {
      id: 'root',
      userVoList: [
        {
          id: '1',
          name: '管理员',
          username: 'admin',
          phone: null,
          showTime: '2025-05-12 18:55:02',
          avatar: '/admin/sys-file/oss/file?fileName=ed2741fdd01b4ce3b43bd70e9405c9b1.jpeg',
          approveDesc: null,
          operType: null,
          status: 2,
          approvalStatus: 2
        }
      ],
      userGroupList: null,
      placeholder: null,
      status: 2,
      approvalStatus: 1,
      name: '发起人',
      type: 0,
      assignedType: null,
      multipleMode: null,
      isApprovalRoute: false,
      selectUser: null,
      multiple: null,
      children: null,
      branch: null
    },
    {
      id: 'madax32ntk86k',
      userVoList: [
        {
          id: '1919665158770573314',
          name: '江培荣',
          username: '17502828828',
          phone: null,
          showTime: null,
          avatar: '/admin/sys-file/qa-dc-saas/7fd33c0367f4422484ece12852c7aa8f.png',
          approveDesc: null,
          operType: null,
          status: 1,
          approvalStatus: 3
        },
        {
          id: '1919675407483973633',
          name: '撒大苏打',
          username: '18146521245',
          phone: null,
          showTime: null,
          avatar: null,
          approveDesc: null,
          operType: null,
          status: 1,
          approvalStatus: 3
        },
        {
          id: '1919682519704403970',
          name: '蒋建军',
          username: '13678205842',
          phone: null,
          showTime: null,
          avatar: null,
          approveDesc: null,
          operType: null,
          status: 1,
          approvalStatus: 3
        },
        {
          id: '1917099163366363137',
          name: '阿斯顿',
          username: '18145906972',
          phone: null,
          showTime: null,
          avatar: null,
          approveDesc: null,
          operType: null,
          status: 1,
          approvalStatus: 3
        },
        {
          id: '1916312982512136194',
          name: '阿斯顿',
          username: '18145719574',
          phone: null,
          showTime: null,
          avatar: null,
          approveDesc: null,
          operType: null,
          status: 1,
          approvalStatus: 3
        }
      ],
      userGroupList: null,
      placeholder: '13678205842,18146521245,17502828828,18145906972,18145719574',
      status: 1,
      approvalStatus: 3,
      name: '审批人',
      type: 1,
      assignedType: 1,
      multipleMode: 2,
      isApprovalRoute: true,
      selectUser: false,
      multiple: null,
      children: null,
      branch: null
    },
    {
      id: 'madazhe8tmzq2',
      userVoList: [],
      userGroupList: null,
      placeholder: null,
      status: 0,
      approvalStatus: 2,
      name: '并行审批',
      type: 5,
      assignedType: null,
      multipleMode: null,
      isApprovalRoute: true,
      selectUser: null,
      multiple: null,
      children: null,
      branch: [
        {
          id: null,
          userVoList: null,
          userGroupList: null,
          placeholder: '满足条件',
          status: 0,
          approvalStatus: 2,
          name: null,
          type: null,
          assignedType: null,
          multipleMode: null,
          isApprovalRoute: null,
          selectUser: null,
          multiple: null,
          children: [
            {
              id: 'madb06iuvi6wy',
              userVoList: [
                {
                  id: '1919682519704403970',
                  name: '蒋建军',
                  username: '13678205842',
                  phone: null,
                  showTime: null,
                  avatar: null,
                  approveDesc: null,
                  operType: null,
                  status: 0,
                  approvalStatus: 2
                },
                {
                  id: '1912104588810575874',
                  name: '向超',
                  username: '15198172532',
                  phone: null,
                  showTime: null,
                  avatar: null,
                  approveDesc: null,
                  operType: null,
                  status: 0,
                  approvalStatus: 2
                }
              ],
              userGroupList: null,
              placeholder: '13678205842,15198172532',
              status: 0,
              approvalStatus: 2,
              name: '审批人',
              type: 1,
              assignedType: 1,
              multipleMode: 1,
              isApprovalRoute: true,
              selectUser: false,
              multiple: null,
              children: null,
              branch: null
            }
          ],
          branch: null
        },
        {
          id: null,
          userVoList: null,
          userGroupList: null,
          placeholder: '满足条件',
          status: 0,
          approvalStatus: 2,
          name: null,
          type: null,
          assignedType: null,
          multipleMode: null,
          isApprovalRoute: null,
          selectUser: null,
          multiple: null,
          children: [
            {
              id: 'madb22e1o2l71',
              userVoList: [
                {
                  id: '1919682519704403970',
                  name: '蒋建军',
                  username: '13678205842',
                  phone: null,
                  showTime: null,
                  avatar: null,
                  approveDesc: null,
                  operType: null,
                  status: 0,
                  approvalStatus: 2
                }
              ],
              userGroupList: null,
              placeholder: '13678205842',
              status: 0,
              approvalStatus: 2,
              name: '审批人',
              type: 1,
              assignedType: 1,
              multipleMode: 1,
              isApprovalRoute: true,
              selectUser: false,
              multiple: null,
              children: null,
              branch: null
            },
            {
              id: 'madb2nyxyjxvt',
              userVoList: [],
              userGroupList: null,
              placeholder: null,
              status: 0,
              approvalStatus: 2,
              name: '审批',
              type: 4,
              assignedType: null,
              multipleMode: null,
              isApprovalRoute: true,
              selectUser: null,
              multiple: null,
              children: null,
              branch: [
                {
                  id: null,
                  userVoList: null,
                  userGroupList: null,
                  placeholder: '(单行文本 等于 11)',
                  status: 0,
                  approvalStatus: 2,
                  name: null,
                  type: null,
                  assignedType: null,
                  multipleMode: null,
                  isApprovalRoute: false,
                  selectUser: null,
                  multiple: null,
                  children: [
                    {
                      id: 'madb4a2dtmbcl',
                      userVoList: [
                        {
                          id: '1919682519704403970',
                          name: '蒋建军',
                          username: '13678205842',
                          phone: null,
                          showTime: null,
                          avatar: null,
                          approveDesc: null,
                          operType: null,
                          status: 0,
                          approvalStatus: 2
                        }
                      ],
                      userGroupList: null,
                      placeholder: '13678205842',
                      status: 0,
                      approvalStatus: 2,
                      name: '审批人',
                      type: 1,
                      assignedType: 1,
                      multipleMode: 1,
                      isApprovalRoute: false,
                      selectUser: false,
                      multiple: null,
                      children: null,
                      branch: null
                    }
                  ],
                  branch: null
                },
                {
                  id: null,
                  userVoList: null,
                  userGroupList: null,
                  placeholder: '默认条件',
                  status: 0,
                  approvalStatus: 2,
                  name: null,
                  type: null,
                  assignedType: null,
                  multipleMode: null,
                  isApprovalRoute: true,
                  selectUser: null,
                  multiple: null,
                  children: [
                    {
                      id: 'madb4nig0m2hk',
                      userVoList: [
                        {
                          id: '1912104588810575874',
                          name: '向超',
                          username: '15198172532',
                          phone: null,
                          showTime: null,
                          avatar: null,
                          approveDesc: null,
                          operType: null,
                          status: 0,
                          approvalStatus: 2
                        }
                      ],
                      userGroupList: null,
                      placeholder: '15198172532',
                      status: 0,
                      approvalStatus: 2,
                      name: '审批人',
                      type: 1,
                      assignedType: 1,
                      multipleMode: 1,
                      isApprovalRoute: true,
                      selectUser: false,
                      multiple: null,
                      children: null,
                      branch: null
                    }
                  ],
                  branch: null
                }
              ]
            },
            {
              id: 'madb53j5fovkq',
              userVoList: [
                {
                  id: '1919682519704403970',
                  name: '蒋建军',
                  username: '13678205842',
                  phone: null,
                  showTime: null,
                  avatar: null,
                  approveDesc: null,
                  operType: null,
                  status: 0,
                  approvalStatus: 2
                }
              ],
              userGroupList: null,
              placeholder: '13678205842',
              status: 0,
              approvalStatus: 2,
              name: '审批人',
              type: 1,
              assignedType: 1,
              multipleMode: 1,
              isApprovalRoute: true,
              selectUser: false,
              multiple: null,
              children: null,
              branch: null
            }
          ],
          branch: null
        }
      ]
    },
    {
      id: 'root',
      userVoList: null,
      userGroupList: null,
      placeholder: null,
      status: 1,
      approvalStatus: 10,
      name: '结束',
      type: null,
      assignedType: null,
      multipleMode: null,
      isApprovalRoute: true,
      selectUser: null,
      multiple: null,
      children: null,
      branch: null
    }
  ],
  traceId: '89c4600fcdd0434a907defa96d0bd770.117.17470476636042233',
  ok: true
}
