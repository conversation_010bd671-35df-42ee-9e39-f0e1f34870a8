<template>
  <page-meta
    class="pkg-todo-user-page-meta"
    background-color="#FFF"
  >
    <view class="pkg-todo-use-wrapper">
      <view
        class="pkg-todo-use-wrapper--item"
        v-for="(user, index) in userList"
        :key="index"
      >
        <view
          class="pkg-todo-use-wrapper--item__avator"
          :style="{
            background: getGradientColor(index) + '!important'
          }"
        >
          <text>
            {{ getLastName(user.name) }}
          </text>
        </view>
        <view class="pkg-todo-use-wrapper--item__name">
          <view class="pkg-todo-use-wrapper--item__name-text">{{ user.name }}</view>
          <view
            class="pkg-todo-use-wrapper--item__name-status"
            v-if="user.approvalStatus"
            :class="{
              'status-icon-wrapper--pending': user.approvalStatus === 3,
              'status-icon-wrapper--rejected': user.approvalStatus === 6,
              'status-icon-wrapper--approved': user.approvalStatus === 4
            }"
          >
            <image
              v-if="user.approvalStatus === 3"
              class="status-icon"
              src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/check-one.png"
            ></image>
            <image
              v-if="user.approvalStatus === 6"
              class="status-icon"
              src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/failed.png"
            ></image>
            <image
              v-if="user.approvalStatus === 4"
              class="status-icon"
              src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/check-one-success.png"
            ></image>
            <text>{{ STATUS_ENUM[user.approvalStatus] }}</text>
          </view>
        </view>
      </view>
    </view>
  </page-meta>
</template>

<script setup lang="js">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app';
import { getGradientColor, getLastName, STATUS_ENUM } from '../audit/constant'

const userList = ref([])
const copy = ref(false)

onLoad(async (options) => {
    userList.value = JSON.parse(options?.list ||'[]')
    copy.value = !!(options?.type === 'copy')
    uni.setNavigationBarTitle({
        title: copy.value ? `自动抄送${userList.value.length}人` : `审批人`,
        backgroundColor: '#FFFFFF'
    })
})
</script>
<style scoped lang="scss">
.pkg-todo-use-wrapper {
  padding: 32rpx;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100vh;
  &--item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 32rpx;
    background-color: #fff;
    gap: 32rpx;
    &__avator {
      flex-shrink: 0;
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      color: white;
      font-size: 12px;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
      // border: 2rpx solid #fff;
    }
    &__name {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 16rpx;
      box-sizing: border-box;
      border-bottom: 1px solid var(--Neutral-border1, #e6eaf0);
      &-text {
        color: #000;
        /* Regular/H5-16 */
        font-family: 'PingFang SC';
        font-size: 32rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
      }
      &-status {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8rpx;
        height: 40rpx;
        padding: 0px 12rpx;
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        background: #7e8694;
        color: #fff;
        .status-icon {
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }
        &.status-icon-wrapper--pending {
          background: var(--background-blue, #e8f5ff);
          color: var(--text-press-blue, #0e59d2);
        }
        &.status-icon-wrapper--rejected {
          background: var(--background-danger, #ffece8);
          color: var(--text-press-danger, #cb272d);
        }
        &.status-icon-wrapper--approved {
          background: var(--background-success, #e8ffea);
          color: var(--text-press-success, #009a29);
        }
      }
    }
  }
}
</style>
