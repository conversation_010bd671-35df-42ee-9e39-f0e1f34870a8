<template>
  <view class="card-wrapper">
    <template v-if="(!$slots.header || $slots.extra) && (title || extra)">
      <view class="card-wrapper--header">
        <view class="card-wrapper--header--title">{{ title }}</view>
        <view
          class="card-wrapper--header--no"
          v-if="!$slots.extra"
        >
          {{ extra }}
        </view>
        <view v-if="$slots.extra"><slot name="extra" /></view>
      </view>
    </template>
    <template v-else>
      <slot name="header" />
    </template>

    <slot />
  </view>
</template>
<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  extra: {
    type: String,
    default: ''
  }
})
</script>
<style lang="scss" scoped>
.card-wrapper {
  border-radius: 20rpx;
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 24rpx;

  &--header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48rpx;
    &--title {
      font-size: 32rpx;
      color: var(--Neutral-primary, #1c2026);
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      flex-shrink: 0;
      margin-right: 16rpx;
    }
    &--no {
      color: var(--Neutral-secondary, #7e8694);
      font-family: 'PingFang SC';
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      flex: 1;
      width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &--content {
    margin-top: 16rpx;

    &--item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44rpx;
      margin-top: 4rpx;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      &--label {
        color: var(--Neutral-secondary, #7e8694);
        width: 156rpx;
        flex-shrink: 0;
      }

      &--value {
        color: var(--Neutral-primary, #1c2026);
        flex-shrink: 0;
        flex: 1;
      }
    }
  }
}
</style>
