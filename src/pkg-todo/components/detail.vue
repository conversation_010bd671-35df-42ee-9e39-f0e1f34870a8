<template>
  <view
    class="pro-detail-wrapper"
    v-if="configFieldList?.length"
  >
    <view
      v-for="item in configFieldList"
      :key="item.label"
      class="pro-detail-wrapper-groupitem"
    >
      <view
        class="pro-detail-wrapper--group"
        v-if="item.label"
        @click.stop="handleClick(item.label)"
      >
        <view class="pro-detail-wrapper--group--label">{{ item.label }}</view>
        <image
          v-if="activePanel.includes(item.label)"
          class="img"
          style="width: 32rpx; height: 32rpx"
          mode="aspectFit"
          src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/caret-up.png"
        ></image>
        <image
          v-else
          class="img"
          style="width: 32rpx; height: 32rpx"
          mode="aspectFit"
          src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/caret-down.png"
        ></image>
      </view>
      <view
        class="pro-detail-wrapper--content"
        v-if="item.children && item.children?.length && activePanel.includes(item.label)"
      >
        <view
          v-for="itemChild in item.children"
          :key="itemChild.prop"
          class="pro-detail-wrapper--content--wrapper"
        >
          <block v-if="showColumnsItem(itemChild)">
            <view class="pro-detail-wrapper--content--item">
              <view
                class="pro-detail-wrapper--content--item--label"
                v-if="itemChild.label"
              >
                {{ renderLabel(itemChild) }}
              </view>

              <template v-if="!$slots[itemChild.prop]">
                <view
                  class="pro-detail-wrapper--content--item--value"
                  v-if="itemChild.type !== 'files'"
                >
                  {{ renserValue(itemChild) }}
                </view>
                <view
                  class="pro-detail-wrapper--content--item--value"
                  v-else
                >
                  <block v-if="info[itemChild.prop]?.length">
                    <view
                      class="pro-detail-wrapper--content--item--value--file"
                      v-for="file in info[itemChild.prop] || []"
                      :key="file.url"
                      @click.stop="handleClickFile(file)"
                    >
                      {{ file.name }}
                    </view>
                  </block>
                  <view v-else>--</view>
                </view>
              </template>
              <template v-else>
                <slot :name="itemChild.prop" />
              </template>
            </view>
          </block>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { computed, ref, onMounted } from 'vue'
import { handleClickFile } from '@/utils/download'

const props = defineProps({
  options: {
    type: Array,
    default: () => {
      return []
    }
  },
  info: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const activePanel = ref([])
const showColumnsItem = (itemChild) => {
  const format = itemChild?.show || null
  if (typeof format === 'function') {
    return format(props?.info)
  }
  return true
}

const renderLabel = (itemChild) => {
  const label = itemChild?.label || ''
  const format = itemChild?.renderLabel || null
  if (typeof format === 'function') {
    return format(props?.info)
  }
  return label
}

const renserValue = (itemChild) => {
  const val = props?.info?.[itemChild.prop] || '--'
  const format = itemChild?.formatter || null
  if (typeof format === 'function') {
    return format(props?.info)
  }
  return val
}

const configFieldList = computed(() => {
  const allGroup = [...new Set((props?.options || [])?.map((item) => item?.group))]?.filter((item) => !!item)
  const result = []
  allGroup?.forEach((item) => {
    result.push({
      label: item,
      children: (props?.options || [])
        ?.filter((itemChild) => itemChild?.group === item && itemChild?.group)
        ?.sort((a, b) => a?.sort - b?.sort)
    })
  })
  // 插入未分组的数据
  const unGroup = (props?.options || [])?.filter((item) => !item?.group)?.sort((a, b) => a?.sort - b?.sort)
  if (unGroup?.length) {
    result.push({ label: '', children: unGroup })
  }
  return result
})

const handleClick = (label) => {
  const index = activePanel.value.findIndex((item) => item === label)
  if (index === -1) {
    activePanel.value.push(label)
  } else {
    activePanel.value.splice(index, 1)
  }
}

onMounted(() => {
  activePanel.value = [...new Set(configFieldList.value?.map((item) => item?.label))]
})
</script>
<script>
export default {
  options: {
    multipleSlots: true,
    virtualHost: true,
    styleIsolation: 'shared'
  }
}
</script>
<style lang="scss" scoped>
.pro-detail-wrapper {
  &-groupitem {
    margin-bottom: 32rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  &--group {
    background: var(--Neutral-lighter, #f6f7f9);
    display: flex;
    height: 80rpx;
    padding: 0rpx 16rpx;
    margin-top: 16rpx;
    justify-content: space-between;
    align-items: center;
    &--label {
      color: var(--Neutral-primary, #1c2026);
      /* Regular/T1-14 */
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      padding-left: 12rpx;
      position: relative;
      &::before {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        content: ' ';
        width: 4rpx;
        height: 24rpx;
        background: var(--brand-blue, #2c6ae3);
        z-index: 1;
      }
    }
  }
  &--content {
    // margin-top: 16rpx;
    // margin-bottom: 32rpx;
    padding-top: 16rpx;
    transition: all 0.3s ease-in-out;
    &--wrapper {
      margin-bottom: 16rpx;
      &:last-child {
        margin-bottom: 0;
      }
    }
    &--item {
      // display: flex;
      // justify-content: space-between;
      // align-items: center;
      // height: 44rpx;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      &--label {
        color: var(--Neutral-secondary, #7e8694);
        text-align: left;
        height: 40rpx;
        font-size: 24rpx;
        // width: 340rpx;
        // flex-shrink: 0;
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // margin-right: 16rpx;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: nowrap;
        word-break: break-all;
      }

      &--value {
        color: var(--Neutral-primary, #1c2026);
        word-wrap: break-word;
        word-break: break-all;
        text-align: left;
        // flex-shrink: 0;
        // flex: 1;
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 1;
        // -webkit-box-orient: vertical;
        // white-space: nowrap;
        // word-break: break-all;

        &--file {
          color: var(--brand-blue, #2c6ae3);
          // height: 40rpx;
          font-size: 28rpx;
          margin-bottom: 12rpx;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
