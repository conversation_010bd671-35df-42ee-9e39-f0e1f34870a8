<template>
  <view class="flow-record">
    <pro-time-line>
      <pro-time-line-item
        nodeTop="4"
        v-for="(row, index) in tableData"
        :key="index"
      >
        <template v-slot:node>
          <image
            class="node-icon"
            v-if="row.hasError"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/node-error.png"
            mode="aspectFit"
          />
          <image
            class="node-icon"
            v-else-if="row.hasDone"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/node-success.png"
            mode="aspectFit"
          />
          <image
            v-else
            class="node-icon"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/node-pending.png"
            mode="aspectFit"
          />
        </template>
        <template v-slot:content>
          <view>
            <view class="header-wrapper">
              <view class="name">{{ row.name }}</view>
              <view
                class="audit-tip"
                v-if="row.type === 5"
              >
                需全部通过审批
              </view>
            </view>
            <view class="userVoList">
              <NodeAvatarList :rowData="row" />
            </view>
          </view>
          <template v-if="row.chNode && row.chNode.length > 0">
            <!-- 这一层代表的是分支 -->
            <view
              v-for="(cItem, cIndex) in row?.chNode || []"
              :key="cIndex"
              class="branch-wrapper"
              :class="{ 'branch-wrapper-cell': row?.chNode?.length > 1 }"
            >
              <!-- 这一层是节点 -->
              <view
                v-for="(nodeItem, nodeIndex) in cItem?.chNode || []"
                :key="nodeIndex"
              >
                <NodeAvatarList :rowData="nodeItem" />
              </view>
            </view>
          </template>
        </template>
      </pro-time-line-item>
    </pro-time-line>
  </view>
</template>

<script setup lang="jsx">
import NodeAvatarList from './avatar.vue'

defineProps({
  tableData: {
    type: Array,
    dafault: () => []
  }
})
</script>

<style scoped lang="scss">
.flow-record {
  width: 100%;
  padding-top: 32rpx;
  .node-icon {
    height: 32rpx;
    width: 32rpx;
  }
  .userVoList {
    margin-top: 12rpx;
  }
  .header-wrapper {
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: 'PingFang SC';
    font-size: 28rpx;
    font-style: normal;
    line-height: 20px; /* 142.857% */
    .name {
      color: var(--Neutral-primary, #1c2026);
      font-weight: 600;
    }
    .audit-tip {
      color: var(--Neutral-secondary, #7e8694);
      font-weight: 400;
    }
  }
  .branch-wrapper-cell {
    margin-bottom: 48rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
