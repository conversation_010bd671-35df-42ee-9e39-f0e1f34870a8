<template>
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 2"
    type="info"
    text="未开始"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 1"
    type="success"
    text="已提交"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 3"
    type="primary"
    text="审批中"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 4"
    type="success"
    text="已通过"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 5"
    type="success"
    text="自动通过"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 6"
    type="error"
    text="已拒绝"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 7"
    type="error"
    text="自动拒绝"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 8"
    type="info"
    text="已结束"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 9"
    type="info"
    text="已抄送"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 10"
    type="info"
    text="未结束"
  />
  <uv-tag
    size="mini"
    bg-color="transparent"
    border-color="transparent"
    v-if="status === 11"
    type="info"
    text="已转办"
  />
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  status: {
    type: String,
    default: ''
  }
})
</script>
<script>
export default {
  options: {
    multipleSlots: true,
    virtualHost: true,
    styleIsolation: 'shared'
  }
}
</script>
<style scoped lang="scss">
:deep(.uv-tag) {
  padding: 0 !important;
  font-size: 24rpx !important;
}
</style>
