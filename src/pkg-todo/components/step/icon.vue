<template>
  <image
    v-if="status === 3"
    class="user-node-status-icon"
    src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/check-one.png"
  ></image>
  <image
    v-if="status === 6"
    class="user-node-status-icon"
    src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/failed.png"
  ></image>
  <image
    v-if="status === 4"
    class="user-node-status-icon"
    src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/check-one-success.png"
  ></image>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  status: {
    type: String,
    default: ''
  }
})
</script>
<script>
export default {
  options: {
    multipleSlots: true,
    virtualHost: true,
    styleIsolation: 'shared'
  }
}
</script>
<style scoped lang="scss">
.user-node-status-icon {
  width: 24rpx;
  height: 24rpx;
  vertical-align: top;
}
</style>
