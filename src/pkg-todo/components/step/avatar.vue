<template>
  <view
    v-if="rowData?.isEnd"
    class="single-user"
  >
    <image
      class="user-avatar"
      src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/avator-system.png"
      mode="aspectFit"
    />
    <view class="user-avatar-content-wrapper">
      <view class="user-avatar-content">
        <view class="user-left">
          <view class="user-name">系统</view>
          <NodeStatus :status="rowData.approvalStatus" />
        </view>
      </view>
      <view
        v-if="userList[0]?.approveDesc"
        class="user-approveDesc"
      >
        {{ userList[0]?.approveDesc }}
      </view>
    </view>
  </view>
  <!-- 抄送人 -->
  <view
    v-if="rowData?.type === 2 && userList?.length >= 1"
    class="single-user"
  >
    <image
      class="user-avatar"
      src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/avator-system.png"
      mode="aspectFit"
    />
    <view class="user-avatar-content-wrapper">
      <view class="user-avatar-content">
        <view class="user-left">
          <view class="user-name">系统</view>
          <view class="copy-name">
            <text>自动抄送</text>
            <text
              class="copy-person"
              @click.stop="handleClickCopyUserList(userList)"
            >
              {{ renderPreson(userList).preson || '' }}
            </text>
            <text v-if="renderPreson(userList).rest > 0">等{{ renderPreson(userList).rest }}人</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 多人 multipleMode 会签1、顺序签3 -->
  <view
    class="multiple-userlist-wrapper"
    v-else-if="userList?.length > 1 && [1, 3].includes(rowData?.multipleMode)"
    @click.stop="handleClickUserList(userList)"
  >
    <view class="user-node-avatar-wrapper">
      <view
        v-for="(user, index) in userList.slice(0, endLength)"
        :key="index"
        class="user-node-avatar"
        :style="{
          // 使用 !important 提升优先级
          background: getGradientColor(index) + '!important',
          marginLeft: index === 0 ? 0 : '-5px',
          zIndex: 1 + index * 2
        }"
      >
        <text v-if="index < endLength - 1">
          {{ getLastName(user.name) }}
        </text>
        <text
          v-else
          style="color: RGB(109, 115, 123)"
        >
          {{ getRestNumber(userList.length) }}
        </text>
        <view
          class="user-node-status-icon"
          v-if="
            userListLast?.approvalStatus && index < endLength - 1 && [3, 4, 6].includes(userListLast?.approvalStatus)
          "
        >
          <NodeIcon :status="userListLast?.approvalStatus" />
        </view>
      </view>
    </view>
    <view class="user-node-status-wrapper">
      <NodeStatus :status="rowData.approvalStatus" />
      <view class="node-tip">需所有人同意</view>
    </view>
  </view>
  <!-- 多人 multipleMode 或签2 -->
  <view
    class="multiple-userlist-wrapper or-userlist-wrapper"
    v-else-if="userList?.length > 1 && [2].includes(rowData?.multipleMode)"
    @click.stop="handleClickUserList(userList)"
  >
    <view class="user-node-avatar-wrapper">
      <view
        v-for="(user, index) in userList"
        :key="index"
        class="user-node-avatar"
        :style="{
          // 使用 !important 提升优先级
          background: getGradientColor(index) + '!important',
          marginLeft: index === 0 ? 0 : '34rpx',
          zIndex: 1
        }"
      >
        <text>
          {{ getLastName(user.name) }}
        </text>
        <text
          class="or-text"
          v-if="index < userList.length - 1"
        >
          或
        </text>
      </view>
    </view>
    <view class="user-node-status-wrapper">
      <NodeStatus :status="rowData.approvalStatus" />
      <view class="node-tip">1人通过即可</view>
    </view>
  </view>
  <view
    v-else-if="userList?.length === 1"
    class="single-user"
  >
    <view
      class="user-avatar-multi"
      :style="{
        background: getGradientColor(0) + '!important'
      }"
    >
      {{ getLastName(userList[0].name) }}

      <view
        class="user-node-status-icon"
        v-if="rowData?.approvalStatus && [3, 4, 6].includes(rowData?.approvalStatus)"
      >
        <NodeIcon :status="rowData.approvalStatus" />
      </view>
    </view>
    <view class="user-avatar-content-wrapper">
      <view class="user-avatar-content">
        <view class="user-left">
          <view class="user-name">{{ userList[0]?.username }}</view>
          <NodeStatus :status="rowData.approvalStatus" />
        </view>
        <view
          class="user-avatar-time"
          v-if="userList[0]?.showTime"
        >
          {{ dayjs(userList[0]?.showTime).format('MM月DD日 HH:mm') }}
        </view>
      </view>
      <view
        v-if="userList[0]?.approveDesc"
        class="user-approveDesc"
      >
        {{ userList[0]?.approveDesc }}
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, computed, ref } from 'vue'
import NodeStatus from './status.vue'
import NodeIcon from './icon.vue'

import dayjs from 'dayjs'

const props = defineProps({
  rowData: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const endLength = ref(4)

const userList = computed(() => {
  return props.rowData?.userVoList || []
})
const userListLast = computed(() => {
  return userList.value?.slice(0, 4).reverse()?.[0]
})

// 预置 5 种深色渐变色
const gradientColors = [
  'linear-gradient(180deg, #B751FF 0%, #8F25DB 100%)',
  'linear-gradient(180deg, #5F8FFF 0%, #3069F0 100%)',
  'linear-gradient(135deg, #F8000D 5.17%, #FF93CE 88.58%)',
  'linear-gradient(180deg, #FF363A 0%, #FF9532 100%)',
  'linear-gradient(180deg, #5F8FFF 0%, #1DBA83 100%)',
  'linear-gradient(180deg, #008B1F 0%, #99C200 100%)'
]
// 生成渐变色，循环使用预置颜色
const getGradientColor = (index) => {
  // 通过取模运算循环使用预置的渐变色
  return gradientColors[index % gradientColors.length]
}
// 获取姓名最后两个字
const getLastName = (name) => {
  return name.slice(-2)
}

const getRestNumber = (num) => {
  return `+${num - 3}` // 假设最多显示 3 个用户
}

const handleClickUserList = (res) => {
  const list = res?.map((item) => {
    return {
      name: item?.name,
      approvalStatus: item?.approvalStatus
    }
  })
  uni.navigateTo({
    url: `/pkg-todo/user/index?list=${JSON.stringify(list)}`
  })
}

const handleClickCopyUserList = (res) => {
  const list = res?.map((item) => {
    return {
      name: item?.name
    }
  })
  if (list?.length > 1) {
    uni.navigateTo({
      url: `/pkg-todo/user/index?list=${JSON.stringify(list)}&type=copy`
    })
  }
}

const renderPreson = (list) => {
  const initNumber = 2
  const preson = list
    ?.slice(0, initNumber)
    ?.map((item) => {
      return item?.name
    })
    ?.join('、')
  const rest = list?.length - initNumber
  return {
    rest,
    preson
  }
}
</script>

<style scoped lang="scss">
.user-node-status-icon {
  position: absolute;
  right: 0px;
  bottom: -6rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: #fff;
  border-radius: 50%;
  overflow: hidden;
}

// 多人样式
.multiple-userlist-wrapper {
  .user-node-avatar-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .user-node-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    color: white;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    border: 2rpx solid #fff;
  }
  .user-node-status-wrapper {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    // gap: 16rpx;
  }
  .node-tip {
    color: var(--text-grey-secondary, #7e8694);
    leading-trim: both;
    text-edge: cap;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    padding-top: 4rpx;
    padding-left: 8rpx;
  }

  // 或签
  &.or-userlist-wrapper {
    .or-text {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      left: 80rpx;
      z-index: 1;

      color: var(--Neutral-secondary, #7e8694);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 14px; /* 116.667% */
    }
  }
}

.single-user {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16rpx;
  .user-avatar {
    flex-shrink: 0;
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
  }
  .user-avatar-multi {
    flex-shrink: 0;
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    color: white;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    border: 2rpx solid #fff;
  }
  .user-name {
    color: var(--Neutral-primary, #1c2026);
    leading-trim: both;
    text-edge: cap;
    font-family: 'PingFang SC';
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
  .copy-name {
    margin-top: 4rpx;
    overflow: hidden;
    color: var(--Neutral-secondary, #7e8694);
    leading-trim: both;
    text-edge: cap;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 133.333% */
    .copy-person {
      color: var(--brand-blue, #2c6ae3);
      padding: 0 4rpx;
    }
  }
  .user-avatar-content-wrapper {
    flex: 1;
    .user-avatar-content {
      // height: 64rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      .user-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
      }
    }
    .user-approveDesc {
      margin-top: 16rpx;
      border-radius: 8rpx;
      padding: 16rpx;
      background: var(--Neutral-lighter, #f6f7f9);
      color: var(--Neutral-primary, #1c2026);
      /* Regular/T1-14 */
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
  .user-avatar-time {
    color: var(--text-grey-secondary, #7e8694);
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 16px; /* 133.333% */
  }
}
</style>
