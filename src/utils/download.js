import { previewImage } from '@/utils/watermark'
// import useOcrHooks from '@/hooks/useOcrHooks'

/**
 * 下载文件
 * @param {string} url - 文件下载地址
 * @param {string} fileName - 下载后的文件名
 * @returns {Promise} - 返回下载结果
 */
export const downloadFile = (url, fileName) => {
  console.log('url', url, fileName)
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.downloadFile({
      url,
      success: (res) => {
        console.log('res', res)
        if (res.statusCode === 200) {
          // 使用新的文件系统API保存文件
          const fs = uni.getFileSystemManager()
          fs.saveFile({
            tempFilePath: res.tempFilePath,
            success: (saveRes) => {
              console.log('saveRes', saveRes)

              resolve({
                success: true,
                savedFilePath: saveRes.savedFilePath
              })
            },
            fail: (err) => {
              console.log('err', err)

              reject({
                success: false,
                error: err
              })
            }
          })
        } else {
          console.log('err-res', res)
          reject({
            success: false,
            error: new Error(`下载失败，状态码：${res.statusCode}`)
          })
        }
      },
      fail: (err) => {
        console.log('err-fail', err)
        reject({
          success: false,
          error: err
        })
      }
    })
    // #endif

    // #ifdef H5
    // H5环境下的下载实现
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    resolve({
      success: true
    })
    // #endif
  })
}

// 检查是否是支持的文档格式
export function isSupportedDocument(url) {
  if (!url) return false
  // 转换为小写并去除空格
  const normalizedName = url?.toLowerCase()?.trim()
  // 支持的文档格式
  const documentExtensions = ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'ppt', 'pptx']
  return documentExtensions.find((ext) => normalizedName.endsWith(ext))
}

// 判断是否是图片
export function isImage(url) {
  const normalizedName = url?.toLowerCase()?.trim()
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  return imageExtensions.find((ext) => normalizedName.endsWith(ext))
}

export function handleClickFile(file) {
  console.log('附件文件', file)
  // const { isImage } = useOcrHooks()
  const url = file.url || ''
  if (!url) {
    uni.$u.toast('文件地址有误')
    return
  }
  // 如果是图片，使用预览图片
  if (isImage(url)) {
    previewImage(url)
    return
  }
  // 如果是支持的文档格式，使用 openDocument 打开
  if (isSupportedDocument(url)) {
    uni.downloadFile({
      url,
      success: function (res) {
        const filePath = res.tempFilePath
        console.log('下载文件成功', res.tempFilePath)
        uni.openDocument({
          filePath,
          showMenu: true,
          fileType: isSupportedDocument(url),
          success: function (res) {
            console.log('打开文档成功', res)
          },
          fail: function (res) {
            console.log('打开文档失败', res)
            uni.$u.toast('该文件类型格式不支持预览')
          }
        })
      },
      fail: function (res) {
        console.log('下载文件失败', res)
        uni.$u.toast('文件下载失败')
      }
    })
    return
  } else {
    uni.downloadFile({
      url,
      success: function (res) {
        const filePath = res.tempFilePath
        console.log('下载文件成功', res.tempFilePath)
        uni.openDocument({
          filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功', res)
          },
          fail: function (res) {
            console.log('打开文档失败', res)
            uni.$u.toast('该文件类型格式不支持预览')
          }
        })
      },
      fail: function (res) {
        console.log('下载文件失败', res)
        uni.$u.toast('文件下载失败')
      }
    })
  }
}
