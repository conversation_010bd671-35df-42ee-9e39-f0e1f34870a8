/**
 * 时间格式化工具函数
 * @param date 时间输入，支持 Date 对象、时间戳、ISO 字符串
 * @param format 输出格式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatDate(date: Date | number | string, format = 'YYYY-MM-DD HH:mm:ss'): string {
  // 统一转换为 Date 对象
  let dateObj: Date
  if (typeof date === 'string') {
    // 处理 iOS 日期格式，将横杠替换为斜杠
    const iosCompatibleDate = date.replace(/-/g, '/')
    dateObj = new Date(iosCompatibleDate)
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else {
    dateObj = date
  }

  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    throw new Error('Invalid date')
  }

  const year = dateObj.getFullYear()
  const month = dateObj.getMonth() + 1
  const day = dateObj.getDate()
  const hours = dateObj.getHours()
  const minutes = dateObj.getMinutes()
  const seconds = dateObj.getSeconds()

  // 补零函数
  const padZero = (num: number): string => num.toString().padStart(2, '0')

  // 替换格式化字符串中的占位符
  return format
    .replace('YYYY', year.toString())
    .replace('MM', padZero(month))
    .replace('DD', padZero(day))
    .replace('HH', padZero(hours))
    .replace('mm', padZero(minutes))
    .replace('ss', padZero(seconds))
}

// 使用示例：
// formatDate(new Date()) // 2024-03-21 14:30:45
// formatDate(1679304645000) // 2024-03-21 14:30:45
// formatDate('2024-03-21T14:30:45Z') // 2024-03-21 14:30:45
// formatDate(new Date(), 'YYYY年MM月DD日 HH:mm:ss') // 2024年03月21日 14:30:45

export function formatDateToTimestamp(date: string, format = 'YYYY-MM-DD HH:mm:ss'): number {
  return new Date(date).getTime()
}

/**
 * 将各种格式的日期字符串转换为时间戳
 * @param dateStr 日期字符串，支持以下格式：
 * - 1967年07月13日
 * - 1967-07-13
 * - 1967/07/13
 * - 1967.07.13
 * @returns 时间戳（毫秒）
 */
export function formatDateStringToTimestamp(dateStr: string): number {
  // 处理中文格式：1967年07月13日
  if (dateStr.includes('年')) {
    const [year, month, day] = dateStr.split(/[年月日]/).filter(Boolean)
    return new Date(Number(year), Number(month) - 1, Number(day)).getTime()
  }

  // 处理其他格式：1967-07-13, 1967/07/13, 1967.07.13
  const separator = dateStr.includes('-') ? '-' : dateStr.includes('/') ? '/' : '.'
  const [year, month, day] = dateStr.split(separator)
  return new Date(Number(year), Number(month) - 1, Number(day)).getTime()
}

// 使用示例：
// formatDateStringToTimestamp('1967年07月13日') // 返回时间戳
// formatDateStringToTimestamp('1967-07-13') // 返回时间戳
// formatDateStringToTimestamp('1967/07/13') // 返回时间戳
// formatDateStringToTimestamp('1967.07.13') // 返回时间戳
