import { uploadImage } from '@/api/app'
import { useUserStore } from '@/stores/user'
import request from '@/utils/request'
import cache from '@/utils/cache'
import { useAppStore } from '@/stores/app'

export function uuid() {
  const s = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'
  return s.join('')
}

export async function uploadFileOSS(file, pure = true) {
  const userStore = useUserStore()
  const { getImageUrl } = useAppStore()

  return new Promise((resolve, reject) => {
    try {
      const { url: fileUrl } = file
      uploadImage(fileUrl, userStore?.token || 'e8b02232-c69d-406b-8432-cfef0091c02f')
        .then((res) => {
          const url = getImageUrl(res?.data?.[0]?.url)
          if (url) {
            if (pure) {
              resolve(url)
            } else {
              resolve({ ossUrl: url, origin: file, oss: res?.data?.[0] })
            }
          } else {
            reject('附件地址不存在')
          }
        })
        .catch((error) => {
          reject(error)
        })
    } catch (error) {
      reject(error)
    }
  })
}

export async function batchUploadFileOSS(files, pure = true) {
  const allUploaderPromise = files?.map((item) => {
    return uploadFileOSS(item, false)
  })
  const list = await Promise.allSettled(allUploaderPromise)
  return list.filter((item) => item.status === 'fulfilled').map((item) => (pure ? item.value.ossUrl : item.value))
}

// 上传文件
export function uploadFile(callback) {
  uni.chooseMessageFile({
    count: 10,
    type: 'all',
    async success(res) {
      const result = await handleUploadFile(res)
      if (result.length > 0 && typeof callback === 'function') {
        callback(result)
      }
    },
    fail(error) {
      console.log('error', error)
    }
  })
}

// 处理选择的文件
export async function handleUploadFile(files) {
  const uploadTasks = files.tempFiles.map((file) => {
    // 跨端路径兼容处理
    const filePath = file.path || file.tempFilePath
    // 获取文件名（小程序需从 tempFilePaths 解析）
    const fileName = file.name || filePath.split('/').pop()
    return uploadToServer(filePath, fileName)
  })
  // 可并行/串行处理上传任务
  return await Promise.all(uploadTasks)
}

// 封装上传方法
export async function uploadToServer(filePath, fileName) {
  const userStore = useUserStore()
  const { getImageUrl } = useAppStore()

  return new Promise((resolve, reject) => {
    try {
      request
        .uploadFile({
          url: '/admin/sys-file/upload',
          filePath,
          name: 'file',
          formData: {
            fileName,
            timestamp: Date.now()
          },
          header: {
            Authorization: `Bearer ${userStore.token}`,
            'TENANT-ID': cache.getTenant()
          }
        })
        .then((res) => {
          if (res.code === 0) {
            resolve({
              // ...(res.data || {}),
              // originFileName: fileName,
              // url: res.data.url,
              // uuid: uuid(),
              // fullUrl: getImageUrl(res.data.url)
              name: fileName || res?.data?.fileName,
              uuid: uuid(),
              url: getImageUrl(res.data.url)
            })
          } else {
            reject(new Error(res?.msg || '服务器响应异常'))
          }
        })
        .catch((error) => {
          reject(error)
        })
    } catch (error) {
      reject(error)
    }
  })
}

/** 本地选择图片的方式 */
// 动态计算压缩质量
export async function calculateQuality(tempFilePaths) {
  const imageInfo = await uni.getImageInfo({ src: tempFilePaths })
  const fileStats = await uni.getFileInfo({ filePath: tempFilePaths })
  const width = imageInfo.width
  const height = imageInfo.height
  const fileSize = fileStats.size
  // 基础参数配置（可根据实际需求调整）
  const MAX_QUALITY = 80 // 最大质量（小图适用）
  const MIN_QUALITY = 55 // 最小质量（避免过度模糊）
  const PIXEL_THRESHOLD = 2000 * 2000 // 像素阈值（200万像素）
  const SIZE_THRESHOLD = 2 * 1024 * 1024 // 文件大小阈值（2MB）
  // 计算像素因子（基于尺寸）
  const pixelFactor = Math.sqrt((width * height) / PIXEL_THRESHOLD)
  // 计算体积因子（基于文件大小）
  const sizeFactor = Math.min(fileSize / SIZE_THRESHOLD, 1)
  // 加权综合因子（尺寸权重 0.7，体积权重 0.3）
  const combinedFactor = 0.7 * pixelFactor + 0.3 * sizeFactor
  // 非线性衰减公式（指数平滑）
  const quality = MAX_QUALITY * Math.exp(-0.5 * combinedFactor)
  // 限制质量范围
  return Math.max(MIN_QUALITY, Math.min(MAX_QUALITY, Math.round(quality)))
}
export async function compressImg(src, quality = 50) {
  return new Promise((resolve, reject) => {
    uni.compressImage({ src, quality, success: resolve, fail: reject })
  })
}
export async function chooseImageUpload() {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1,
      // mediaType: ['image'],
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      success: async (res) => {
        const tempFilePaths = res.tempFilePaths[0]
        const tempFiles = res.tempFiles[0]
        const size = !!(tempFiles.size / 1024 > 1024 * 2)
        if (size) {
          try {
            // 动态计算质量值
            const quality = await calculateQuality(tempFilePaths)
            const comRes = await compressImg(tempFilePaths, quality)
            if (comRes?.errMsg !== 'compressImage:ok') {
              reject('图片压缩失败')
            }
            resolve(comRes?.tempFilePath)
          } catch (error) {
            reject(error)
          }
        } else {
          resolve(tempFilePaths)
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
/** 本地选择图片的方式 */
