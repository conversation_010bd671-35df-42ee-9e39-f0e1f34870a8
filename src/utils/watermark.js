import { uploadToServer } from '@/utils/uploader'
import { formatDate } from '@/utils/dateFormat'
import { useUserStore } from '@/stores/user'
// import wechatOa from '@/utils/wechat'

/**
 * 图片水印工具类
 * 支持微信小程序环境
 */

/**
 * 预览图片
 * @param {String|Array} urls 图片地址或地址数组
 * @param {Number} current 当前显示图片的索引
 */
export const previewImage = (urls, current = 0) => {
  uni.previewImage({
    urls: Array.isArray(urls) ? urls : [urls],
    current: current,
    longPressActions: {
      itemList: ['发送给朋友', '保存图片', '收藏'],
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      success: function (data) {},
      fail: function (err) {
        uni.showToast({
          title: err.errMsg,
          icon: 'none'
        })
      }
    },
    success: () => {
      console.log('预览图片成功')
    },
    fail: (err) => {
      console.error('预览图片失败：', err)
    }
  })
}

/**
 * 生成带水印的图片（多行文本增强版）
 * @param {Object} options 配置项
 * @param {String} options.imagePath 原图路径
 * @param {Array} options.texts 水印文本数组，每个元素可以是字符串或对象 {text: '文本', style: {样式配置}, prefixImage: {图片配置}}
 * @param {Object} options.texts[].prefixImage 前置图片配置
 * @param {String} options.texts[].prefixImage.src 图片路径
 * @param {Number} options.texts[].prefixImage.width 图片宽度
 * @param {Number} options.texts[].prefixImage.height 图片高度
 * @param {Number} options.texts[].prefixImage.marginRight 图片与文字的间距
 * @param {Object} options.style 全局水印样式
 * @param {Object} options.style.offsetX 相对于左下角的水平偏移量（正值向右偏移，负值向左偏移）
 * @param {Object} options.style.offsetY 相对于底部的垂直偏移量
 * @param {Object} options.overlay 水印蒙层配置
 * @param {String} options.overlay.color 蒙层颜色
 * @param {Number} options.overlay.opacity 蒙层透明度
 * @param {Number} options.overlay.padding 蒙层内边距
 * @param {Number} options.overlay.radius 蒙层圆角
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 */
export const createWatermark = (options) => {
  const { imagePath, texts = [], style = {}, overlay = {}, success, fail } = options
  // 默认样式
  const defaultStyle = {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    rotate: 0,
    textAlign: 'left',
    textBaseline: 'bottom',
    padding: 20,
    repeat: false,
    repeatGap: 200,
    offsetX: 0,
    offsetY: 0,
    lineHeight: 1.5,
    maxWidth: null
  }

  // 默认前置图片配置
  const defaultPrefixImage = {
    width: 20,
    height: 20,
    marginRight: 5
  }

  // 默认蒙层配置
  const defaultOverlay = {
    color: '#000000',
    opacity: 0.3,
    padding: 10,
    radius: 4
  }

  const finalStyle = { ...defaultStyle, ...style }
  const finalOverlay = { ...defaultOverlay, ...overlay }

  // 获取图片信息
  uni.getImageInfo({
    src: imagePath,
    success: async (imageInfo) => {
      try {
        // 创建离屏画布
        const canvas = uni.createOffscreenCanvas({
          type: '2d',
          width: imageInfo.width,
          height: imageInfo.height
        })
        const ctx = canvas.getContext('2d')

        // 创建并加载图片
        const image = canvas.createImage()
        await new Promise((resolve, reject) => {
          image.onload = resolve
          image.onerror = reject
          image.src = imagePath
        })

        // 绘制原图
        ctx.drawImage(image, 0, 0, imageInfo.width, imageInfo.height)

        // 处理文本配置
        const processedTexts = texts.map((item) => {
          if (typeof item === 'string') {
            return {
              text: item,
              style: {},
              prefixImage: null
            }
          }
          return {
            text: item.text,
            style: item.style || {},
            prefixImage: item.prefixImage ? { ...defaultPrefixImage, ...item.prefixImage } : null
          }
        })

        // 计算最大文本宽度和总高度
        let maxTextWidth = 0
        let totalHeight = 0
        const allLines = [] // 存储所有文本行

        processedTexts.forEach((item) => {
          const itemStyle = { ...finalStyle, ...item.style }
          ctx.font = `${itemStyle.fontSize}px sans-serif`

          // 处理文本换行
          const lines = []
          if (itemStyle.maxWidth) {
            const words = item.text.split('')
            let line = ''
            for (let i = 0; i < words.length; i++) {
              const testLine = line + words[i]
              const metrics = ctx.measureText(testLine)
              if (metrics.width > itemStyle.maxWidth && i > 0) {
                lines.push(line)
                line = words[i]
              } else {
                line = testLine
              }
            }
            lines.push(line)
          } else {
            lines.push(item.text)
          }

          allLines.push({ lines, style: itemStyle, prefixImage: item.prefixImage })
          const width = Math.max(...lines.map((line) => ctx.measureText(line).width))
          const prefixImageWidth = item.prefixImage ? item.prefixImage.width + item.prefixImage.marginRight : 0
          maxTextWidth = Math.max(maxTextWidth, width + prefixImageWidth)
          totalHeight += itemStyle.fontSize * itemStyle.lineHeight * lines.length + itemStyle.padding
        })

        // 计算水印区域位置（以左下角为基准点）
        const watermarkX = finalStyle.offsetX
        const watermarkY = imageInfo.height - finalStyle.offsetY

        // 绘制蒙层
        ctx.save()
        ctx.fillStyle = finalOverlay.color
        ctx.globalAlpha = finalOverlay.opacity

        // 绘制圆角矩形蒙层
        const overlayX = watermarkX - finalOverlay.padding
        const overlayY = watermarkY - totalHeight - finalOverlay.padding
        const overlayWidth = maxTextWidth + finalOverlay.padding * 2
        const overlayHeight = totalHeight + finalOverlay.padding * 2

        // 绘制圆角矩形
        ctx.beginPath()
        ctx.moveTo(overlayX + finalOverlay.radius, overlayY)
        ctx.lineTo(overlayX + overlayWidth - finalOverlay.radius, overlayY)
        ctx.quadraticCurveTo(overlayX + overlayWidth, overlayY, overlayX + overlayWidth, overlayY + finalOverlay.radius)
        ctx.lineTo(overlayX + overlayWidth, overlayY + overlayHeight - finalOverlay.radius)
        ctx.quadraticCurveTo(
          overlayX + overlayWidth,
          overlayY + overlayHeight,
          overlayX + overlayWidth - finalOverlay.radius,
          overlayY + overlayHeight
        )
        ctx.lineTo(overlayX + finalOverlay.radius, overlayY + overlayHeight)
        ctx.quadraticCurveTo(
          overlayX,
          overlayY + overlayHeight,
          overlayX,
          overlayY + overlayHeight - finalOverlay.radius
        )
        ctx.lineTo(overlayX, overlayY + finalOverlay.radius)
        ctx.quadraticCurveTo(overlayX, overlayY, overlayX + finalOverlay.radius, overlayY)
        ctx.closePath()
        ctx.fill()
        ctx.restore()

        // 绘制水印文本
        let totalProcessedHeight = 0
        allLines.forEach(({ lines, style: itemStyle, prefixImage }) => {
          ctx.save()

          // 设置当前文本样式
          ctx.font = `${itemStyle.fontSize}px sans-serif`
          ctx.fillStyle = itemStyle.color
          ctx.textAlign = itemStyle.textAlign
          ctx.textBaseline = itemStyle.textBaseline

          // 计算当前文本块的总高度
          const textBlockHeight = itemStyle.fontSize * itemStyle.lineHeight * lines.length

          // 绘制每一行文本
          lines.forEach((line, index) => {
            // 计算当前行的Y坐标
            const lineY =
              watermarkY - totalProcessedHeight - (lines.length - index - 1) * itemStyle.fontSize * itemStyle.lineHeight

            // 如果有前置图片，先绘制图片
            if (prefixImage && index === 0) {
              const prefixImageObj = canvas.createImage()
              prefixImageObj.src = prefixImage.src
              ctx.drawImage(
                prefixImageObj,
                watermarkX,
                lineY - prefixImage.height,
                prefixImage.width,
                prefixImage.height
              )
              // 绘制文本，考虑图片宽度和间距
              ctx.fillText(line, watermarkX + prefixImage.width + prefixImage.marginRight, lineY)
            } else {
              ctx.fillText(line, watermarkX, lineY)
            }
          })

          // 更新已处理的总高度
          totalProcessedHeight += textBlockHeight + itemStyle.padding
          ctx.restore()
        })

        // 将离屏画布转换为图片
        const tempFilePath = await new Promise((resolve, reject) => {
          uni.canvasToTempFilePath({
            canvas: canvas,
            success: (res) => resolve(res.tempFilePath),
            fail: reject
          })
        })

        success && success(tempFilePath)
      } catch (error) {
        fail && fail(error)
      }
    },
    fail: (err) => {
      fail && fail(err)
    }
  })
}

/**
 * 批量添加水印
 * @param {Array} imagePaths 图片路径数组
 * @param {Object} options 水印配置
 * @returns {Promise} 返回Promise对象
 */
export const batchAddWatermark = (imagePaths, options) => {
  return new Promise((resolve, reject) => {
    const results = []
    let completed = 0

    imagePaths.forEach((imagePath, index) => {
      createWatermark({
        imagePath,
        ...options,
        success: (result) => {
          results[index] = result
          completed++
          if (completed === imagePaths.length) {
            resolve(results)
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  })
}

export async function addWriteOffAttachmentWatermark(imagePath) {
  const userStore = useUserStore()
  const name = userStore?.userInfo?.sysUser?.name || userStore?.userInfo?.sysUser?.username

  return new Promise((resolve, reject) => {
    createWatermark({
      imagePath,
      texts: [
        {
          text: `${name}`,
          style: {}
        },
        {
          text: `${formatDate(Date.now(), 'YYYY-MM-DD HH:mm')}`,
          style: {}
        }
      ],
      style: {
        fontSize: 22,
        color: '#ffffff',
        textAlign: 'left',
        textBaseline: 'bottom',
        padding: 12,
        lineHeight: 1.1,
        offsetX: 30, // 从右下角向左偏移20像素
        offsetY: 30 // 从底部向上偏移20像素
      },
      overlay: {
        color: 'rgb(204, 204, 204)',
        opacity: 0.5,
        padding: 8,
        radius: 8
      },
      success: async (tempFilePath) => {
        console.log('水印图片生成成功：', tempFilePath)
        try {
          resolve(await uploadToServer(tempFilePath))
        } catch (error) {
          reject(error)
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}
