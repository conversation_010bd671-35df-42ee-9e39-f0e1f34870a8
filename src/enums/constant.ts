// 待办页面-TAB-审批状态
export enum TodoTabAuditStatusEnum {
  INCOMPLETE = 'INCOMPLETE', // 待审批
  END = 'END' // 已审批
}

// 待办页面-TAB-审批状态-列表
export const TodoTabAuditStatusList = [
  {
    name: '待审批',
    value: TodoTabAuditStatusEnum.INCOMPLETE
  },
  {
    name: '已审批',
    value: TodoTabAuditStatusEnum.END
  }
]

// 待办页面-批量审核-审批结果
export const TodoBatchAuditResultEnum = {
  APPROVED: 'APPROVED', // 审批通过
  REJECTED: 'REJECTED', // 审批不通过 已驳回
  REVOKED: 'REVOKED' // 已撤销
}

// 待办页面-批量审核-审批结果-数字
export const TodoBatchAuditResultNumberEnum = {
  APPROVED: 4, // 审批通过
  REJECTED: 6 // 审批不通过 已驳回
}

export const TodoBatchAuditResultList = [
  {
    label: '审批通过',
    value: TodoBatchAuditResultEnum.APPROVED
  },
  {
    label: '审批不通过',
    value: TodoBatchAuditResultEnum.REJECTED
  }
]

// 审批详情-审批记录-审批状态
export const TodoAuditRecordStatusEnum = {
  PASS: 'PASS', // 审批通过
  REJECT: 'REJECT', // 审批不通过
  AUDITING: 'AUDITING' // 审批中
}
// 审批详情-审批记录-审批状态
export const TodoAuditRecordStatusEnumObj = {
  [TodoAuditRecordStatusEnum.PASS]: '审批通过',
  [TodoAuditRecordStatusEnum.REJECT]: '审批不通过',
  [TodoAuditRecordStatusEnum.AUDITING]: '审批中'
}

export enum LoginTypeEnum {
  MOBILE = 'mobile',
  ACCOUNT = 'account'
}

export enum LoginWayEnum {
  ACCOUNT = 1,
  MOBILE = 2
}

// 待办操作类型
export const TodoOperationTypeEnum = {
  AUDIT: 'AUDIT',
  AUDIT_DETAIL: 'AUDIT_DETAIL'
}
