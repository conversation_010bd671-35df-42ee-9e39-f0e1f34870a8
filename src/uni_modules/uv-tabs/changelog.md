## 1.0.9（2023-12-01）
1. 修复current为字符串activeStyle不生效的BUG
2. 优化文档
## 1.0.8（2023-10-13）
1. 优化点击一个选项，change事件重复派发的问题
## 1.0.7（2023-09-14）
1. 优化首次加载时，处理下划线会有左到右的过渡效果
## 1.0.6（2023-09-13）
1. 修复设置lineWidth未带单位产生的误差BUG
## 1.0.5（2023-06-23）
添加uv-icon依赖
## 1.0.4（2023-06-16）
1. 增加customStyle参数
## 1.0.3（2023-06-12）
1. activeStyle设置字体大小，导致下划线位置不对的BUG，增加this.$nextTick机制
## 1.0.2（2023-05-23）
1. 修复nvue中不滚动的BUG
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-tabs 标签选项卡
