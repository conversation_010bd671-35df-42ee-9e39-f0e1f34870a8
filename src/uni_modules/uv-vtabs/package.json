{"id": "uv-vtabs", "displayName": "uv-vtabs 垂直选项卡 全面兼容vue3+2、app、h5、小程序等多端", "version": "1.0.8", "description": "uv-vtabs 垂直分类组件主要用于分类选择，简单配置即可使用，左右自动进行联动，不用自己再去做复杂的计算，组件内部已经完成相关计算。支持联动和不联动，商品分类，灵活配置，多端兼容开箱即用。", "keywords": ["uv-vtabs", "uvui", "uv-ui", "垂直分类", "垂直选项卡"], "repository": "", "engines": {}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uv-ui-tools", "uv-badge"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}