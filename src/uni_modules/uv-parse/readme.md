## Parse 富文本解析器

> **组件名：uv-parse**

该组件一般用于富文本解析场景，比如解析文章内容，商品详情，带原生`HTML`标签的各类字符串等，此组件和`uni-app`官方的`rich-text`组件功能有重合之处，但是也有不同的地方。

该插件只提供富文本的解析，该功能已经足够丰富。如果需要富文本的编辑，可使用`uniapp`官方提供的组件。

# <a href="https://www.uvui.cn/components/parse.html" target="_blank">查看文档</a>

## [下载完整示例项目](https://ext.dcloud.net.cn/plugin?name=uv-ui)

### [更多插件，请关注uv-ui组件库](https://ext.dcloud.net.cn/plugin?name=uv-ui)

<a href="https://ext.dcloud.net.cn/plugin?name=uv-ui" target="_blank">

![image](https://mp-a667b617-c5f1-4a2d-9a54-683a67cff588.cdn.bspapp.com/uv-ui/banner.png)

</a>

#### 如使用过程中有任何问题反馈，或者您对uv-ui有一些好的建议，欢迎加入uv-ui官方交流群：<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>