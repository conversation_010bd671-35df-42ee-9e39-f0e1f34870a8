## Code 验证码输入框

> **组件名：uv-code**

考虑到用户实际发送验证码的场景，可能是一个按钮，也可能是一段文字，提示语各有不同，所以本组件不提供界面显示，只提供倒计时文本，由用户将文本嵌入到具体的场景。

### <a href="https://www.uvui.cn/components/code.html" target="_blank">查看文档</a>

### [完整示例项目下载 | 关注更多组件](https://ext.dcloud.net.cn/plugin?name=uv-ui)

#### 如使用过程中有任何问题，或者您对uv-ui有一些好的建议，欢迎加入 uv-ui 交流群：<a href="https://ext.dcloud.net.cn/plugin?id=12287" target="_blank">uv-ui</a>、<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>
