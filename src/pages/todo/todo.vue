<template>
  <page-meta
    class="todo-page-meta"
    background-color="#F2F3F5"
  >
    <view class="todo-wrapper">
      <pro-token-expired v-if="tokenExpired" />
      <block v-else>
        <view class="todo-wrapper--floatheader">
          <view
            class="todo-wrapper--space"
            :style="{ height: '24px' }"
          ></view>
          <view class="todo-wrapper--header">
            <view class="todo-wrapper--header__title">待办</view>
            <uv-search
              bgColor="#FFF"
              placeholder="输入分组名、流程实例ID、流程名称、发起人"
              placeholderColor="#7E8694"
              height="64"
              shape="round"
              v-model="filterForm.keywords"
              @change="changeSearchHandler"
              @search="keywordSearchHandler"
            />
          </view>
          <view class="todo-wrapper--tab">
            <view class="todo-wrapper--tab--wrapper">
              <view
                class="todo-wrapper--tab--wrapper--item"
                :class="{ active: filterForm.taskStatus === item.value }"
                v-for="(item, index) in TodoTabAuditStatusList"
                :key="index"
                @tap.stop="updateTab(item)"
              >
                <view class="todo-wrapper--tab--wrapper--item__label">
                  {{ item.name }} ({{ todoCount[item.value] }})
                </view>
                <view class="todo-wrapper--tab--wrapper--item__line"></view>
              </view>
            </view>

            <view
              class="todo-wrapper--tab--batch"
              v-if="!isBatchAudit && filterForm.taskStatus === TodoTabAuditStatusEnum.INCOMPLETE"
              @tap.stop="updateBatchAudit(true)"
            >
              <image
                class="todo-wrapper--tab--batch__icon"
                src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/icon-gavel.png"
              ></image>
              <view class="todo-wrapper--tab--batch__label">批量审批</view>
            </view>
          </view>
        </view>
        <view
          class="todo-wrapper--list"
          :style="{
            paddingTop: '104px'
          }"
        >
          <uv-empty
            v-if="isEmpty && !loading"
            text="暂无数据"
            :customStyle="{ height: '66vh' }"
          ></uv-empty>
          <view v-if="!isEmpty && !loading">
            <view
              @tap.stop="toPageHandler(item)"
              class="todo-wrapper--list--container"
              v-for="item in list"
              :key="item.taskId"
            >
              <block v-if="isBatchAudit">
                <image
                  v-if="checkList.includes(item.taskId)"
                  class="todo-wrapper--list--icon"
                  src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-checked.png"
                ></image>
                <image
                  v-else
                  class="todo-wrapper--list--icon"
                  src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-unchecked.png"
                ></image>
              </block>
              <view class="todo-wrapper--list--item">
                <view class="todo-wrapper--list--item__header">
                  <view class="todo-wrapper--list--item__header__label">
                    {{ item.processName || '--' }}
                  </view>
                  <block v-if="filterForm.taskStatus === TodoTabAuditStatusEnum.INCOMPLETE">
                    <view class="todo-wrapper--list--item__header__tag warning">待审批</view>
                  </block>
                  <block v-else>
                    <view
                      v-if="String(item.approvalStatus) === '1' && String(item.processStatus) === '2'"
                      class="todo-wrapper--list--item__header__tag success"
                    >
                      审批通过
                    </view>
                    <view
                      v-else-if="String(item.approvalStatus) === '2' && String(item.processStatus) === '2'"
                      class="todo-wrapper--list--item__header__tag error"
                    >
                      已驳回
                    </view>
                    <view
                      v-else-if="String(item.processStatus) === '1'"
                      class="todo-wrapper--list--item__header__tag success"
                    >
                      审批中
                    </view>
                  </block>
                </view>
                <!-- TODO 需要确认 -->
                <view class="todo-wrapper--list--item__sublabel">
                  {{ item.processInstanceId || '--' }}
                </view>
                <view class="todo-wrapper--list--item__content">
                  <view class="todo-wrapper--list--item__content__label">申请人员</view>
                  <view class="todo-wrapper--list--item__content__value">
                    {{ item.rootUserName || '--' }}
                  </view>
                </view>
                <view class="todo-wrapper--list--item__content">
                  <view class="todo-wrapper--list--item__content__label">申请时间</view>
                  <view class="todo-wrapper--list--item__content__value">
                    {{ item.startTime ? formatDate(item.startTime, 'YYYY-MM-DD HH:mm') : '--' }}
                  </view>
                </view>
                <view
                  class="todo-wrapper--list--item__remark"
                  v-if="item.lastComment && String(item.approvalStatus) === '1'"
                >
                  {{ item.lastComment }}
                </view>
                <view
                  class="todo-wrapper--list--item__remark error"
                  v-if="item.lastComment && String(item.approvalStatus) === '2'"
                >
                  {{ item.lastComment }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <view
      class="todo-wrapper--bottom"
      v-if="isBatchAudit"
    >
      <view class="todo-wrapper--bottom__wrapper">
        <view
          class="todo-wrapper--bottom__wrapper--checkbox"
          @tap.stop="toggleAllHandler"
        >
          <image
            v-if="isAllChecked"
            class="todo-wrapper--bottom__wrapper--checkbox--icon"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-checked.png"
          ></image>
          <image
            v-else
            class="todo-wrapper--bottom__wrapper--checkbox--icon"
            src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-unchecked.png"
          ></image>
          <text class="todo-wrapper--bottom__wrapper--checkbox--label">全选</text>
        </view>
        <view
          class="todo-wrapper--bottom__wrapper--cancle"
          @tap.stop="updateBatchAudit(false)"
        >
          取消
        </view>
        <view class="todo-wrapper--bottom__wrapper--confirm">
          <uv-button
            @tap.stop="batchAuditHandler"
            type="primary"
            color="#2C6AE3"
            shape="circle"
            size="normal"
            :disabled="checkList.length === 0"
            :customStyle="{
              border: 'none',
              fontFamily: 'PingFang SC',
              fontSize: '28rpx',
              fontStyle: 'normal',
              fontWeight: '400'
            }"
          >
            批量审批 ({{ checkList.length }})
          </uv-button>
        </view>
      </view>
    </view>
    <pro-bottom-drawer
      :config="defaultConfigs"
      ref="proDrawerRef"
      @onConfirm="handleConfirm"
    >
      <view class="batch-audit--wrapper">
        <view class="batch-audit--wrapper--label">审批结果</view>
        <pro-radio-group
          :config="TodoBatchAuditResultList"
          v-model="auditForm.approveCondition"
        ></pro-radio-group>
        <view class="batch-audit--wrapper--label mt-48">审批意见</view>
        <view class="batch-audit--wrapper--textarea">
          <uv-input
            type="textarea"
            v-model="auditForm.approveDesc"
            placeholder="请输入您的审批意见"
            border="none"
            bgColor="#f6f7f9"
            placeholderStyle="color: #7E8694;font-size: 28rpx;"
          ></uv-input>
        </view>
      </view>
    </pro-bottom-drawer>

    <tabbar />
  </page-meta>
</template>

<script setup lang="js">
import { ref, computed } from 'vue'
import { useUserStore } from "@/stores/user";
import { queryMineTask } from '@/api/todo';
import { formatDate } from '@/utils/dateFormat'
import { TodoTabAuditStatusList, TodoTabAuditStatusEnum, TodoBatchAuditResultList, TodoBatchAuditResultEnum, TodoOperationTypeEnum } from '@/enums/constant';
import { useProList } from "@/hooks/useProList";
import {
  onPullDownRefresh,
  onReachBottom,
  onShow,
  onLoad,
} from "@dcloudio/uni-app";
import { useAudit } from '@/hooks/useAudit'

const {submit} = useAudit()
const userStore = useUserStore();
const checkList = ref([]);
const loading = ref(false);
const submitLoading = ref(false);
const pageOptions = ref({})
const isBatchAudit = ref(false);
const proDrawerRef = ref();
const defaultConfigs = computed(() => {
    return {
        title: "批量审批",
        confirmWithClose: false,
        cancelBtnText: "取消",
        confirmBtnText: "确认",
        showCancelBtn: true,
        showConfirmBtn: true,
        confirmDisabled: submitLoading.value,
    };
});

const todoCount = ref({
    [TodoTabAuditStatusEnum.INCOMPLETE]: 0,
    [TodoTabAuditStatusEnum.END]: 0,
})

const filterForm = ref({
    taskStatus: TodoTabAuditStatusEnum.INCOMPLETE,
    keywords: null,
    // approvedStatusList: [1,2],
    searchExtendList: [
        "PROCESS_INSTANCE_ID",
        "PROCESS_DEFINITION_NAME",
        "PROCESS_GROUP_NAME",
        "INITIATOR_NAME",
        "PHONE_NUMBER"
    ],
})
const extParams = computed(() => {
    if (filterForm.value?.taskStatus === TodoTabAuditStatusEnum.END) {
        return {
            approvedStatusList: [1, 2],
            ...filterForm.value,
        }
    }
    return {
        ...filterForm.value,
    }
})
const tokenExpired = computed(() => {
    return userStore?.tokenExpired
})

const auditForm = ref({
    approveDesc: null,
    approveCondition: TodoBatchAuditResultEnum.APPROVED,
    taskList: [],
})


function toggleHandler(taskId) {
    if (checkList.value.includes(taskId)) {
        checkList.value = checkList.value.filter(item => item !== taskId)
    } else {
        checkList.value.push(taskId)
    }
}

function updateTab(item) {
    if (item.value === filterForm.value.taskStatus) {
        return
    }

    // 回复初始状态
    checkList.value = [];
    isBatchAudit.value = false;

    filterForm.value.taskStatus = item.value;


    refresh()
}

function updateBatchAudit(status) {
    checkList.value = [];

    isBatchAudit.value = status
}

const { list, refresh, isEmpty, remoteMethod } = useProList({
  apiFn: queryMineTask,
  responseHandler: (list) => {
    return list;
  },
  extParams: extParams,
  lazyLoad: true,
  addPageEvent: false,
});



const isAllChecked = computed(() => {
    return checkList.value.length === list.value.length
})

function toggleAllHandler() {
    if (isAllChecked.value) {
        checkList.value = []
    } else {
        checkList.value = list.value.map(item => item.taskId)
    }
}

function toPageHandler(item) {
    if (isBatchAudit.value) {
        toggleHandler(item.taskId)
        return
    }

    const itemJSON = JSON.stringify({
        flowId: item.flowId,
        processName: item.processName,
        processInstanceId: item.processInstanceId,
        rootUserName: item.rootUserName,
        startTime: item.startTime,
        taskId: item.taskId,
        nodeId: item.nodeId,
        processStatus: item.processStatus,
        lastComment: item.lastComment,
        approvalStatus: item.approvalStatus,
    })

    if (filterForm.value.taskStatus === TodoTabAuditStatusEnum.INCOMPLETE && !isBatchAudit.value) {
        uni.navigateTo({
            url: `/pkg-todo/audit/index?taskItem=${itemJSON}&operationType=${TodoOperationTypeEnum.AUDIT}`
        })
        return
    }

    if (filterForm.value.taskStatus === TodoTabAuditStatusEnum.END) {
        uni.navigateTo({
            url: `/pkg-todo/audit/index?taskItem=${itemJSON}&operationType=${TodoOperationTypeEnum.AUDIT_DETAIL}`
            // url: `/pkg-todo/detail/index?taskItem=${itemJSON}`
        })
        return
    }
}

function batchAuditHandler() {
    // // TODO 需要删除
    // proDrawerRef.value.open();

    if (checkList.value.length === 0) {
        return
    }

    // 重置表单
    auditForm.value.taskList = [];
    auditForm.value.approveDesc = null;
    auditForm.value.approveCondition = TodoBatchAuditResultEnum.APPROVED;

    proDrawerRef.value.open();
}

function changeSearchHandler(value) {
    filterForm.value.keywords = value || null
    refresh()
    getTodoCount()
}

function keywordSearchHandler(value) {
    filterForm.value.keywords = value || null
    refresh()
    getTodoCount()
}


async function handleConfirm() {
    submitLoading.value = true;
    // const params = {
    //     ...auditForm.value,
    //     taskList: list.value.filter(item => checkList.value.includes(item.taskId))?.map(item => ({taskId: item.taskId, nodeId: item.nodeId, processInstanceId: item.processInstanceId})),
    // }
    try {
        // await completeTaskBatch(params)
        await submit(checkList.value, auditForm.value)
        proDrawerRef.value.close();
        uni.showToast({
            title: '操作成功',
            icon: 'success',
        })

        isBatchAudit.value = false;
        checkList.value = [];
        filterForm.value.keywords = null;

        refresh()
        getTodoCount()
    // eslint-disable-next-line no-empty
    } catch (error) {
        uni.showToast({
            title: error?.message || error?.msg || '操作失败',
            icon: 'none',
        })
    }
    submitLoading.value = false;
}


function buildParams(status) {
    if (status === TodoTabAuditStatusEnum.INCOMPLETE) {
        return {
            ...filterForm.value,
            current: 1,
            page: 1,
            size: 1,
            taskStatus: TodoTabAuditStatusEnum.INCOMPLETE,
        }
    }
    return {
        ...filterForm.value,
        current: 1,
        page: 1,
        size: 1,
        taskStatus: TodoTabAuditStatusEnum.END,
        approvedStatusList: [1, 2],
    }
}
function getTodoCount() {
    const paramsIncomplete = buildParams(TodoTabAuditStatusEnum.INCOMPLETE)
    const paramsEnd = buildParams(TodoTabAuditStatusEnum.END)
    queryMineTask(paramsIncomplete)?.then(res => {
        todoCount.value[TodoTabAuditStatusEnum.INCOMPLETE] = res?.data?.total || 0
    })
    queryMineTask(paramsEnd)?.then(res => {
        todoCount.value[TodoTabAuditStatusEnum.END] = res?.data?.total || 0
    })
}


onShow(async () => {
    await userStore.checkToken();
    if (!tokenExpired.value) {
        loading.value = true;
        getTodoCount()
        await refresh()
        loading.value = false;
    }
})
onLoad((options) => {
    pageOptions.value = options
})

onPullDownRefresh(() => {
  console.log("onPullDownRefresh");
  if (!tokenExpired.value) {
    refresh();
  }
});

onReachBottom(() => {
  console.log("onReachBottom");
  if (!tokenExpired.value) {
    remoteMethod();
  }
});
</script>

<style lang="scss">
.todo-wrapper {
  // overflow: hidden;
  // display: flex;
  // flex-direction: column;
  // height: 100vh;
  // overflow: auto;

  &--floatheader {
    position: fixed;
    z-index: 20;
    background-color: #f2f3f5;
    width: 100%;
    left: 0;
  }

  &--header {
    display: flex;
    // padding: 16rpx 0;
    padding-right: 220rpx;
    box-sizing: border-box;
    overflow: hidden;
    height: 64rpx;
    box-sizing: border-box;

    &__title {
      color: var(--Neutral-primary, #1c2026);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 400;
      margin: 0 24rpx;
      flex-shrink: 0;
      height: 64rpx;
      line-height: 64rpx;
    }

    &__input {
      position: relative;
      flex-shrink: 0;
      flex: 1;

      :deep(.uv-input__content-input) {
        height: 64rpx !important;
        padding-left: 48rpx !important;
      }

      &--icon {
        position: absolute;
        left: 24rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 24rpx;
        height: 24rpx;
        z-index: 1;
      }
    }
  }

  &--tab {
    padding: 0 24rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &--wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &--item {
        flex: 1;
        flex-shrink: 0;
        padding: 0 32rpx;
        height: 80rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        position: relative;

        &__label {
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 28rpx;
          font-style: normal;
          font-weight: 500;
          height: 44rpx;
          line-height: 44rpx;
          color: rgba(0, 0, 0, 0.65);
          width: max-content;
        }

        &__line {
          border-top-left-radius: 8rpx;
          border-top-right-radius: 8rpx;
          width: 24px;
          height: 3px;
          opacity: 0;
          position: absolute;
          bottom: 6rpx;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1;
        }

        &.active {
          .todo-wrapper--tab--wrapper--item__label {
            color: var(--brand-blue, #2c6ae3);
          }

          .todo-wrapper--tab--wrapper--item__line {
            opacity: 1;
            background-color: var(--brand-blue, #2c6ae3);
          }
        }
      }
    }

    &--batch {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &__icon {
        width: 32rpx;
        height: 32rpx;
      }

      &__label {
        color: var(--Neutral-primary, #1c2026);
        font-family: 'PingFang SC';
        font-size: 28rpx;
        color: var(--brand-blue, #2c6ae3);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        margin-left: 8rpx;
      }
    }
  }

  &--list {
    padding: 0 32rpx;
    // padding-bottom: 168rpx;
    // padding-bottom: calc(constant(var(safe-area-inset-bottom, 0) + 100rpx));
    // padding-bottom: calc(var(safe-area-inset-bottom, 0) + 100rpx);
    // flex: 1;
    // overflow: auto;
    box-sizing: border-box;
    // height: 400px;

    &--container {
      display: flex;
      align-items: center;
    }

    &--icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    &--item {
      background-color: #fff;
      border-radius: 20rpx;
      background: #fff;
      padding: 32rpx;
      margin-bottom: 24rpx;
      flex: 1;

      &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48rpx;

        &__label {
          color: var(--Neutral-primary, #1c2026);
          text-align: center;
          /* Medium/T5-16 */
          font-family: 'PingFang SC';
          font-size: 32rpx;
          font-style: normal;
          font-weight: 600;
        }

        &__tag {
          border-radius: 4px;
          background: var(--background-warning, #fff7e8);
          padding: 0 12rpx;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: max-content;
          color: var(--text-press-warning, #d25f00);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          &.success {
            background: var(--background-success, #e8ffea);
            color: var(--text-press-success, #00b42a);
          }
          &.error {
            background: var(--background-error, #ffece8);
            color: var(--text-press-error, #f53f3f);
          }
          &.warning {
            background: var(--background-warning, #fff7e8);
            color: var(--text-press-warning, #d25f00);
          }
        }
      }

      &__sublabel {
        color: var(--Neutral-secondary, #7e8694);
        font-family: 'PingFang SC';
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        height: 40rpx;
        line-height: 40rpx;
      }

      &__remark {
        border-radius: 4px;
        background: var(--Neutral-lighter, #f6f7f9);
        padding: 5px 8px;
        color: var(--Neutral-regular, #505762);
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        margin-top: 16rpx;
        &.error {
          border-radius: 4px;
          background: var(--background-opacity-danger, rgba(245, 63, 63, 0.1));
          color: var(--text-danger, #f53f3f);
        }
      }

      &__content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44rpx;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        margin-top: 8rpx;

        &:first-child {
          margin-top: 16rpx;
        }

        &__label {
          color: var(--Neutral-secondary, #7e8694);
          width: 156rpx;
          flex-shrink: 0;
        }

        &__value {
          color: var(--Neutral-primary, #1c2026);
          text-align: left;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
        }
      }
    }
  }

  &--bottom {
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 1000;

    &__wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0rpx 32rpx;
      height: 100rpx;
      background: var(--Grey-el-color-white, #fff);
      box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.05);
      width: 100vw;
      box-sizing: border-box;

      &--checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &--icon {
          width: 48rpx;
          height: 48rpx;
        }

        &--label {
          color: var(--Neutral-regular, #505762);
          /* Regular/T1-14 */
          font-family: 'PingFang SC';
          font-size: 28rpx;
          font-style: normal;
          font-weight: 400;
          margin-left: 8rpx;
        }
      }

      &--cancle {
        color: var(--brand-blue, #2c6ae3);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
      }
    }
  }
}

.batch-audit--wrapper {
  padding: 32rpx 0;

  .mt-48 {
    margin-top: 48rpx;
  }

  &--label {
    color: var(--Neutral-primary, #1c2026);
    font-family: 'PingFang SC';
    font-size: 26rpx;
    font-style: normal;
    font-weight: 400;
    margin-bottom: 16rpx;
  }

  &--textarea {
    padding: 24rpx;
    background-color: #f6f7f9;
    border-radius: 12rpx;

    :deep(.uv-input__content) {
      background-color: #f6f7f9 !important;
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}
</style>
