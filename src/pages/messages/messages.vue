<template>
  <view class="messages-page">
    <!-- 导航栏 -->
    <uv-navbar
      title="消息中心"
      :safeAreaInsetTop="true"
      :fixed="false"
      leftIcon="arrow-left"
      :autoBack="true"
      bgColor="#FFFFFF"
      :titleStyle="{ color: '#1C2026', fontSize: '18px', fontWeight: '600' }"
      leftIconColor="#1C2026"
      @leftClick="goBack"
    />

    <!-- 搜索区域 -->
    <view class="search-section">
      <uv-search
        v-model="searchKeyword"
        placeholder="请输入关键词搜索"
        :showAction="false"
        bgColor="#F6F7F9"
        shape="round"
        :clearabled="true"
        searchIcon="search"
        searchIconColor="#7E8694"
        color="#000000"
        placeholderColor="rgba(0, 0, 0, 0.45)"
        height="44"
        @search="handleSearch"
        @input="handleInput"
      />
    </view>

    <!-- 消息列表 -->
    <view class="content">
      <view class="message-list">
        <!-- 未读消息 -->
        <view class="message-card" @click="openMessage(0)">
          <view class="message-header">
            <text class="message-title">这是消息的标题</text>
            <view class="unread-dot"></view>
          </view>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>

        <!-- 已读消息 -->
        <view class="message-card read" @click="openMessage(1)">
          <text class="message-title">这是消息的标题这是消息的标题这是消息的标题</text>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>

        <!-- 已读消息 -->
        <view class="message-card read" @click="openMessage(2)">
          <text class="message-title">这是消息的标题</text>
          <text class="message-content">从病虫害探测、土壤墒情监测智能系统，再到耕、种、管、收等智能机器人，近几年来，越来越多具有自主知识产权的新技术、智能产品开始出现在农业场景中。</text>
          <view class="message-footer">
            <text class="message-time">2025/02/12 12:22</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 搜索关键词
const searchKeyword = ref('')

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 搜索处理
const handleSearch = (value: string) => {
  console.log('搜索:', value)
  // 这里可以实现搜索逻辑
}

// 输入处理
const handleInput = (value: string) => {
  searchKeyword.value = value
}

// 打开消息详情
const openMessage = (index: number) => {
  console.log('打开消息', index)
  // 这里可以跳转到消息详情页面
  // uni.navigateTo({
  //   url: `/pages/message-detail/message-detail?id=${index}`
  // })
}
</script>

<style lang="scss" scoped>
.messages-page {
  width: 100%;
  min-height: 100vh;
  background: #F0F2F5;
  overflow: hidden;
}

/* 搜索区域 */
.search-section {
  background: #FFFFFF;
  padding: 8px 12px 12px;
  margin-bottom: 12px;
}

/* 内容区域 */
.content {
  padding: 0 12px;
  margin-top: 0;

  .message-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

/* 消息卡片 */
.message-card {
  background: #FFFFFF;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;

  .message-header {
    display: flex;
    align-items: center;
    width: 100%;

    .message-title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      line-height: 1.5;
      color: #1D2129;
    }

    .unread-dot {
      width: 8px;
      height: 8px;
      background: #F53F3F;
      border-radius: 50%;
      margin-left: 4px;
    }
  }

  .message-title {
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
    color: #4E5969;
    width: 100%;
    height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .message-content {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.57;
    color: #1D2129;
    width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .message-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;

    .message-time {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.57;
      color: #7E8694;
      text-align: center;
    }
  }

  // 已读消息样式
  &.read {
    .message-title {
      color: #4E5969;
      font-weight: 600;
    }

    .message-content {
      color: #4E5969;
    }
  }
}
</style>
