<template>
  <view class="bg-white min-h-full flex flex-col items-center px-[40rpx] pt-[40rpx] box-border">
    <view class="w-full">
      <uv-form
        borderBottom
        :label-width="150"
      >
        <uv-form-item
          label="手机号"
          borderBottom
        >
          <uv-input
            class="flex-1"
            v-model="formData.mobile"
            border="none"
            placeholder="请输入手机号码"
          />
        </uv-form-item>
        <uv-form-item
          label="验证码"
          borderBottom
        >
          <uv-input
            class="flex-1"
            v-model="formData.code"
            placeholder="请输入验证码"
            border="none"
          />
          <view
            class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[180rpx]"
            @click="sendSms"
          >
            <uv-code
              ref="uCodeRef"
              :seconds="60"
              @change="codeChange"
              change-text="x秒"
            />
            {{ codeTips }}
          </view>
        </uv-form-item>
      </uv-form>
      <view class="mt-[40rpx]">
        <uv-button
          type="primary"
          shape="circle"
          @click="handleConfirm"
        >
          确定
        </uv-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { userBindMobile } from '@/api/user'
import { smsSend } from '@/api/app'
import { reactive, ref, shallowRef } from 'vue'
import { useUserStore } from '@/stores/user'
const uCodeRef = shallowRef()
const codeTips = ref('')

const userStore = useUserStore()
const codeChange = (text: string) => {
  codeTips.value = text
}

const formData = reactive({
  type: 'bind',
  mobile: '',
  code: '',
})
const sendSms = async () => {
  if (!formData.mobile) return uni.$uv.toast('请输入手机号码')
  if (uCodeRef.value?.canGetCode) {
    await smsSend(formData.mobile)
    uni.$uv.toast('发送成功')
    uCodeRef.value?.start()
  }
}
const handleConfirm = async () => {
  if (!formData.mobile) return uni.$uv.toast('请输入手机号码')
  if (!formData.code) return uni.$uv.toast('请输入验证码')
  await userBindMobile(formData, { token: userStore.temToken })
  uni.$uv.toast('绑定成功')
  userStore.login(userStore.temToken!)
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}
</script>

<style lang="scss">
page {
  height: 100%;
}
</style>
