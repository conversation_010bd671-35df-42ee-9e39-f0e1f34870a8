<template>
  <view class="register">
    <uv-form
      labelPosition="left"
      :model="form"
      :rules="rules"
      :labelWidth="120"
      ref="formRef"
    >
      <uv-form-item
        label="地区"
        prop="area"
        borderBottom
      >
        <ProAreaPicker
          v-model="form.area"
          :options="options"
          @change="onPickerChange"
        />
      </uv-form-item>
    </uv-form>
    <!-- 底部操作栏 -->
    <view class="form-submit">
      <uv-button
        type="primary"
        shape="circle"
        :loading="loading"
        @click="handleSubmit"
        customStyle="width: 95%;"
      >
        提交
      </uv-button>
    </view>
  </view>
</template>

<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app'
import { ref, nextTick } from 'vue'
import ProAreaPicker from '@/components/pro-area-picker/pro-area-picker.vue'

const formRef = ref(null)
const loading = ref(false)
const form = ref({})

onLoad((query) => {
  console.log(query)
})

onReady(() => {})

const handleSubmit = () => {
  console.log('form.value>>>>', form.value)
}
const onPickerChange = (e) => {
  console.log(e)
}

const options = ref([
  {
    name: '一级1',
    value: '111',
    children: [
      {
        name: '二级1',
        value: '1111',
        children: [
          { name: '三级1', value: '11111' },
          { name: '三级2', value: '11211' }
        ]
      },
      {
        name: '二级2',
        value: '1121',
        children: [
          { name: '三级1', value: '11211' },
          { name: '三级2', value: '11221' }
        ]
      },
      { name: '二级2', value: '1121', children: [] }
    ]
  },
  { name: '一级2', value: '112', children: [{ name: '二级11', value: '1111' }] },
  {
    name: '一级11',
    value: '1111',
    children: [
      {
        name: '二级11',
        value: '11111',
        children: [
          { name: '三级11', value: '111111' },
          { name: '三级21', value: '112111' }
        ]
      },
      {
        name: '二级21',
        value: '11211',
        children: [
          { name: '三级11', value: '112111' },
          { name: '三级21', value: '112211' }
        ]
      },
      { name: '二级21', value: '11211', children: [] }
    ]
  },
  { name: '一级25', value: '1125', children: [{ name: '二级115', value: '11115' }] },
  { name: '一级26', value: '1126', children: [{ name: '二级116', value: '11116' }] },
  { name: '一级27', value: '1127', children: [{ name: '二级117', value: '11117' }] },
  { name: '一级251', value: '11251', children: [{ name: '二级1151', value: '111151' }] },
  { name: '一级263', value: '11263', children: [{ name: '二级1163', value: '111163' }] },
  { name: '一级275', value: '11275', children: [{ name: '二级1175', value: '111175' }] },
  { name: '一级256', value: '11257', children: [{ name: '二级1157', value: '111157' }] },
  { name: '一级269', value: '11269', children: [{ name: '二级1169', value: '111169' }] },
  { name: '一级272', value: '11272', children: [{ name: '二级1172', value: '111172' }] },
  { name: '一级254', value: '11254', children: [{ name: '二级1154', value: '111154' }] },
  { name: '一级2612', value: '112612', children: [{ name: '二级11612', value: '1111612' }] },
  { name: '一级2713', value: '112713', children: [{ name: '二级11713', value: '11117112' }] },
  { name: '一级2512', value: '112512', children: [{ name: '二级11512', value: '1111512' }] },
  { name: '一级2615', value: '112615', children: [{ name: '二级11615', value: '1111615' }] },
  { name: '一级2718', value: '112718', children: [{ name: '二级11718', value: '1111719' }] }
])
</script>

<style lang="scss" scoped>
.register {
  width: 1oovw;
  min-height: 100vh;
  background-color: #f4f5f7;
  padding-top: 180rpx;
}

.form-submit {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  display: flex;
  justify-content: center;
  z-index: 999;
  align-items: center;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-top: 16px;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
