<template>
  <view class="register bg-white min-h-full flex flex-col items-center px-[64rpx] box-border">
    <view class="w-full flex flex-col gap-[64rpx]">
      <view :class="['text-[18px] mt-[212rpx] h-[48px] leading-[24px]', state === 1 ? 'text-left' : 'text-center']">
        {{ steps[state] }}
      </view>
      <uv-form borderBottom>
        <uv-form-item
          v-if="state === 0"
          borderBottom
        >
          <uv-input
            v-model="formData.mobile"
            border="none"
            type="number"
            :maxlength="11"
            placeholderStyle="font-size:16px"
            customStyle="padding-top:3px;padding-bottom:3px;padding-left:0;"
            placeholder="请输入手机号码"
            @input="handleMobileInput"
          />
        </uv-form-item>
        <uv-form-item
          v-if="state === 1"
          borderBottom
        >
          <uv-input
            class="flex-1"
            v-model="formData.code"
            border="none"
            placeholderStyle="font-size:16px"
            customStyle="padding-top:3px;padding-bottom:3px;padding-left:0;"
            placeholder="请输入验证码"
          />
          <template #right>
            <view
              class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3"
              @click="sendSms"
            >
              <uv-code
                ref="uCodeRef"
                :seconds="60"
                @change="codeChange"
                change-text="x秒"
              />
              <text :class="formData.mobile ? 'text-primary' : 'text-muted'">
                {{ codeTips }}
              </text>
            </view>
          </template>
        </uv-form-item>

        <!-- 第三步：新密码 -->
        <template v-if="state === 2">
          <uv-form-item borderBottom>
            <uv-input
              class="flex-1"
              :type="showPassword ? 'text' : 'password'"
              v-model="formData.password"
              border="none"
              placeholderStyle="font-size:16px"
              customStyle="padding-top:3px;padding-bottom:3px;padding-left:0;"
              placeholder="密码"
            >
              <template #suffix>
                <uv-icon
                  :name="showPassword ? 'eye-off-outline' : 'eye'"
                  @click="showPassword = !showPassword"
                  size="16"
                />
              </template>
            </uv-input>
          </uv-form-item>
          <uv-form-item borderBottom>
            <uv-input
              class="flex-1"
              :type="showPasswordConfirm ? 'text' : 'password'"
              v-model="formData.passwordConfirm"
              border="none"
              placeholderStyle="font-size:16px"
              customStyle="padding-top:3px;padding-bottom:3px;padding-left:0;"
              placeholder="确认密码"
            >
              <template #suffix>
                <uv-icon
                  :name="showPasswordConfirm ? 'eye-off-outline' : 'eye'"
                  @click="showPasswordConfirm = !showPasswordConfirm"
                  size="16"
                />
              </template>
            </uv-input>
          </uv-form-item>
        </template>
      </uv-form>
      <view class="flex flex-col gap-[32rpx] mt-[16rpx]">
        <uv-button
          type="primary"
          customStyle="border-radius:8px;padding:8px;font-size:16px;line-height:24px;"
          :disabled="!isMobileValid"
          @click="handleNext"
        >
          下一步
        </uv-button>
        <uv-button
          v-if="state > 0"
          type="info"
          :customStyle="`border-radius:8px;padding:8px;font-size:16px;line-height:24px;
				  }`"
          @click="handlePre"
        >
          上一步
        </uv-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { sendSmsCodeToUpsertPwd, upsertUserPwdBySms } from '@/api/app'
import { computed, reactive, ref, shallowRef, watch } from 'vue'

const uCodeRef = shallowRef()
const codeTips = ref('')
const state = ref(0)
const showPassword = ref(false)
const showPasswordConfirm = ref(false)

const formData = reactive({
  mobile: '',
  code: '',
  password: '',
  passwordConfirm: '',
})

// 监听state变化，动态设置页面标题
watch(
  state,
  (newState) => {
    let title = '找回密码'
    if (newState === 1) {
      title = '忘记密码'
    } else if (newState === 2) {
      title = '设置新密码'
    }
    uni.setNavigationBarTitle({
      title,
    })
  },
  { immediate: true }
)

const steps = computed<string[]>(() => {
  const masked = formData.mobile ? filterMobile(formData.mobile) : ''
  return ['输入注册账号的手机号', `若您的手机号 ${masked} 现在可接收短信，请点击获取验证码`, '设置新密码']
})

function filterMobile(mobile: string): string {
  if (!/^\d{11}$/.test(mobile)) {
    return mobile
  }
  return mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
}

function validateMobile(mobile: string): boolean {
  return /^1[3-9]\d{9}$/.test(mobile)
}

const isMobileValid = computed(() => {
  return !!formData.mobile && validateMobile(formData.mobile)
})

const codeChange = (text: string) => {
  codeTips.value = text
}

const sendSms = async () => {
  if (!formData.mobile) return
  if (uCodeRef.value?.canGetCode) {
    const { msg } = await sendSmsCodeToUpsertPwd({ mobile: formData.mobile })
    if (msg) {
      uni.$uv.toast(msg || '发送失败')
      return
    }
    uni.$uv.toast('发送成功')
    uCodeRef.value?.start()
  }
}

const handleNext = async () => {
  // 第一步校验手机号
  if (state.value === 0) {
    if (!formData.mobile) {
      return uni.$uv.toast('请输入手机号码')
    }
    if (!validateMobile(formData.mobile)) {
      return uni.$uv.toast('请输入正确的手机号码')
    }
  }

  // 第二步校验验证码
  if (state.value === 1) {
    if (!formData.code) {
      return uni.$uv.toast('请输入验证码')
    }
  }

  // 第三步校验密码
  if (state.value === 2) {
    if (!formData.password) {
      return uni.$uv.toast('请输入密码')
    }
    if (!formData.passwordConfirm) {
      return uni.$uv.toast('请输入确认密码')
    }
    if (formData.password !== formData.passwordConfirm) {
      return uni.$uv.toast('两次输入的密码不一致')
    }
    // 调用重置接口
    const { mobile: phone, code: smsCode, password: newpassword1, passwordConfirm: newpassword2 } = formData
    await upsertUserPwdBySms({
      phone,
      smsCode,
      newpassword1,
      newpassword2,
    })
    uni.$uv.toast('重置成功')
    // 返回或跳转
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
    return
  }

  if (state.value < 2) {
    state.value++
  }
}
const handlePre = () => {
  if (state.value > 0) {
    state.value--
  }
}

const handleMobileInput = (value: string | number) => {
  // 只保留数字，并限制为11位
  const numericValue = String(value).replace(/\D/g, '').slice(0, 11)
  formData.mobile = numericValue
}
</script>

<style lang="scss">
page {
  height: 100%;
}
</style>
