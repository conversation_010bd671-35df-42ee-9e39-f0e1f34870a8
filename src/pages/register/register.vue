<template>
  <view class="register bg-white min-h-full flex flex-col items-center px-[40rpx] pt-[100rpx] box-border">
    <view class="w-full">
      <view class="text-2xl font-medium mb-[60rpx]">注册新用户</view>
      <uv-form
        borderBottom
        :label-width="150"
      >
        <uv-form-item
          label="手机号"
          borderBottom
        >
          <uv-input
            class="flex-1"
            v-model="formData.phone"
            border="none"
            placeholder="请输入手机号码"
            @blur="validatePhone"
          />
        </uv-form-item>
        <uv-form-item
          label="验证码"
          borderBottom
        >
          <uv-input
            class="flex-1"
            v-model="formData.mobileCode"
            placeholder="请输入验证码"
            border="none"
          />
          <view
            class="border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[180rpx]"
            @click="sendSms"
          >
            <uv-code
              ref="uCodeRef"
              :seconds="60"
              @change="codeChange"
              change-text="x秒"
            />
            <text :class="formData.phone ? 'text-primary' : 'text-muted'">
              {{ codeTips }}
            </text>
          </view>
        </uv-form-item>
        <uv-form-item
          label="新密码"
          borderBottom
        >
          <uv-input
            class="flex-1"
            type="password"
            v-model="formData.password"
            placeholder="6-20位数字+字母或符号组合"
            border="none"
            @input="checkPasswordStrength"
            @focus="passwordFocused = true"
            @blur="passwordFocused = false"
          />
        </uv-form-item>
        <view
          v-if="passwordFocused"
          class="mt-2 password-strength"
        >
          <view class="strength-bar">
            <view
              :class="['strength-level', strengthClass]"
              :style="{ width: strengthWidth }"
            ></view>
          </view>
          <text
            class="strength-text"
            :class="strengthClass"
          >
            {{ strengthText }}
          </text>
        </view>
        <uv-form-item
          label="确认密码"
          borderBottom
        >
          <uv-input
            class="flex-1"
            type="password"
            v-model="formData.password2"
            placeholder="再次输入新密码"
            border="none"
          />
        </uv-form-item>
      </uv-form>
      <view class="mt-[100rpx]">
        <uv-button
          type="primary"
          shape="circle"
          @click="handleConfirm"
        >
          确定
        </uv-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { register } from '@/api/account'
import { smsSend } from '@/api/app'
import { reactive, ref, computed, shallowRef } from 'vue'

const uCodeRef = shallowRef()
const codeTips = ref('')
const formData = reactive({
  phone: '',
  mobileCode: '',
  password: '',
  password2: '',
})

const strengthLevel = ref(0)
const strengthText = computed(() => {
  switch (strengthLevel.value) {
    case 1:
      return '简单'
    case 2:
      return '中等'
    case 3:
      return '复杂'
    default:
      return '弱'
  }
})

const strengthClass = computed(() => {
  switch (strengthLevel.value) {
    case 1:
      return 'strength-weak'
    case 2:
      return 'strength-medium'
    case 3:
      return 'strength-strong'
    default:
      return ''
  }
})

const strengthWidth = computed(() => `${strengthLevel.value * 33.33}%`)

const checkPasswordStrength = () => {
  const password = formData.password
  let strength = 0

  if (password.length >= 8) strength++
  if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  strengthLevel.value = strength
}

// 添加手机号验证函数
const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

const codeChange = (text: string) => {
  codeTips.value = text
}

const validatePhone = () => {
  if (formData.phone && !isValidPhone(formData.phone)) {
    uni.$uv.toast('请输入正确的手机号码')
  }
}

const sendSms = async () => {
  if (!formData.phone) return uni.$uv.toast('请输入手机号码')
  if (!isValidPhone(formData.phone)) return uni.$uv.toast('请输入正确的手机号码')
  if (uCodeRef.value?.canGetCode) {
    await smsSend(formData.phone)
    uni.$uv.toast('发送成功')
    uCodeRef.value?.start()
  }
}

const handleConfirm = async () => {
  if (!formData.phone) return uni.$uv.toast('请输入手机号码')
  if (!isValidPhone(formData.phone)) return uni.$uv.toast('请输入正确的手机号码')
  if (!formData.mobileCode) return uni.$uv.toast('请输入手机号码')
  if (!formData.password) return uni.$uv.toast('请输入密码')
  if (strengthLevel.value < 2) return uni.$uv.toast('密码强度不够，请设置更复杂的密码')
  if (!formData.password2) return uni.$uv.toast('请输入确认密码')
  if (formData.password != formData.password2) return uni.$uv.toast('两次输入的密码不一致')
  const { code, msg } = await register(formData)

  if (code === 1) {
    uni.$uv.toast(msg)
  } else {
    uni.$uv.toast('操作成功')
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  }
}

const passwordFocused = ref(false)
</script>

<style lang="scss">
page {
  height: 100%;
}

.password-strength {
  margin-top: 8rpx;
  transition: opacity 0.3s ease;

  .strength-bar {
    height: 4rpx;
    background-color: #e0e0e0;
    margin-bottom: 4rpx;
  }

  .strength-level {
    height: 100%;
    transition: width 0.3s ease;
  }

  .strength-text {
    font-size: 24rpx;
  }

  .strength-weak {
    background-color: #ff4d4f;
    color: #ff4d4f;
  }

  .strength-medium {
    background-color: #faad14;
    color: #faad14;
  }

  .strength-strong {
    background-color: #52c41a;
    color: #52c41a;
  }
}
</style>
