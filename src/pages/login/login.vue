<template>
  <view class="login-wrapper">
    <view class="logo">
      <uv-image
        :src="LoGoImg"
        height="63"
        width="211"
        bgColor="#fff"
      />
    </view>
    <view class="fixed flex justify-end setting">
      <uv-icon
        v-if="tenantList.length"
        name="setting"
        class="text-primary"
        :size="24"
        @click="showActionSheet"
      ></uv-icon>
    </view>
    <view class="from">
      <uv-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        errorType="toast"
        borderBottom
      >
        <template v-if="loginWay == LoginWayEnum.ACCOUNT">
          <uv-form-item
            prop="username"
            borderBottom
          >
            <uv-input
              customStyle="padding-top:3px;padding-bottom:3px"
              v-model="formData.username"
              border="none"
              placeholder="输入账号"
              :placeholderStyle="placeholderStyle"
            />
          </uv-form-item>
          <uv-form-item
            prop="password"
            customStyle="margin-top:8px"
            borderBottom
          >
            <uv-input
              customStyle="padding-top:3px;padding-bottom:3px"
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="密码"
              border="none"
              :placeholderStyle="placeholderStyle"
            >
              <template #suffix>
                <uv-icon
                  :name="showPassword ? 'eye-off-outline' : 'eye'"
                  @click="showPassword = !showPassword"
                  size="16"
                />
              </template>
            </uv-input>
          </uv-form-item>
        </template>
        <template v-if="loginWay == LoginWayEnum.MOBILE">
          <uv-form-item
            prop="mobile"
            borderBottom
          >
            <uv-input
              customStyle="padding-top:3px;padding-bottom:3px"
              v-model="formData.mobile"
              border="none"
              placeholder="请输入手机号码"
              :maxlength="11"
              @input="(value: string | number) => formData.mobile = String(value).slice(0, 11)"
              :placeholderStyle="placeholderStyle"
            />
          </uv-form-item>
          <uv-form-item
            prop="code"
            customStyle="margin-top:8px;display: flex;align-items: center;"
            style="margin-top: 8px; display: flex; align-items: center"
            borderBottom
          >
            <uv-input
              customStyle="padding-top:3px;padding-bottom:3px"
              v-model="formData.code"
              placeholder="请输入验证码"
              border="none"
              :placeholderStyle="placeholderStyle"
            />
            <template #right>
              <view
                class="border-l border-solid border-0 border-light pl-3 leading-4 ml-3"
                @click="sendSms"
              >
                <uv-code
                  ref="uCodeRef"
                  :seconds="60"
                  @change="codeChange"
                  change-text="x秒"
                />
                <text :class="formData.mobile ? 'text-primary' : 'text-muted'">
                  {{ codeTips }}
                </text>
              </view>
            </template>
          </uv-form-item>
        </template>
      </uv-form>
      <view class="flex items-center justify-center mt-[40rpx] text-[12px]">
        <uv-checkbox-group :value="checkboxValue">
          <uv-checkbox
            name="checked"
            shape="circle"
            size="14"
            activeColor="#19be6b"
            @change="handleCheckAgreement"
          >
            <view class="flex items-center">
              <text>已阅读并同意</text>
              <navigator
                url="/pages/agreement/user"
                hover-class="none"
                class="text-primary"
              >
                《用户协议》
              </navigator>
              <text>和</text>
              <navigator
                url="/pages/agreement/privacy"
                hover-class="none"
                class="text-primary"
              >
                《隐私协议》
              </navigator>
            </view>
          </uv-checkbox>
        </uv-checkbox-group>
      </view>
      <view class="mt-[40rpx]">
        <uv-button
          type="success"
          customStyle="border-radius:8px;padding:8px;font-size:16px;line-height:24px;"
          color="#1677FF"
          :disabled="!isFormValid || !isCheckAgreement"
          @click="handleLogin(formData.scene)"
        >
          登录
        </uv-button>
      </view>

      <view class="text-content flex justify-center items-center forget-pwd-text">
        <navigator
          url="/pages/forget_pwd/forget_pwd"
          hover-class="none"
          class="text-primary"
        >
          忘记密码
        </navigator>
      </view>
    </view>
    <view class="flex justify-center items-center">
      <view
        class="icon-button-content"
        @click="changeLoginWay(LoginTypeEnum.ACCOUNT, LoginWayEnum.ACCOUNT)"
        v-if="loginWay == LoginWayEnum.MOBILE"
      >
        <view class="login-icon-btn">
          <image
            src="/src/static/images/icon/icon_lock.svg"
            mode=""
            style="width: 20px; height: 20px"
          ></image>
        </view>
        <view>账号密码登录</view>
      </view>
      <view
        class="icon-button-content"
        @click="changeLoginWay(LoginTypeEnum.MOBILE, LoginWayEnum.MOBILE)"
        v-if="loginWay == LoginWayEnum.ACCOUNT"
      >
        <view class="login-icon-btn">
          <image
            src="/src/static/images/icon/icon_phone.svg"
            mode=""
            style="width: 20px; height: 20px"
          ></image>
        </view>
        <view>验证码登录</view>
      </view>
    </view>
    <mplogin-popup
      v-model:show="showLoginPopup"
      :logo="websiteConfig.logo"
      :title="websiteConfig.name"
      @update="handleUpdateUser"
    />
    <uv-popup
      ref="popupRef"
      mode="bottom"
      :round="10"
      :safeAreaInsetBottom="true"
    >
      <view class="bg-white">
        <view class="flex items-center justify-center py-4 text-base font-medium border-b border-light">租户列表</view>
        <scroll-view
          scroll-y
          class="tenant-list-scroll"
        >
          <uv-list>
            <uv-list-item
              border
              v-for="item in tenantList"
              :key="item.id"
              :customStyle="{ height: '40px', 'line-height': '40px', padding: '0 20px' }"
            >
              <uv-text
                :text="item.name"
                :type="item.selected ? 'success' : ''"
                bold
                :customStyle="{ flex: 1 }"
                :suffixIcon="item.selected ? 'checkbox-mark' : ''"
                iconStyle="color: #19be6b;"
                @click="handleSelectTenant(item)"
              ></uv-text>
            </uv-list-item>
          </uv-list>
        </scroll-view>
      </view>
    </uv-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, shallowRef, watch, computed } from 'vue'
import { debounce } from 'lodash-es'
import LoGoImg from '@/static/images/logo/ayn-logo-full.svg'
import { getTenantsByLogin, userEdit } from '@/api/user'
import { mobileLogin, accountLogin, getJsCode, postMiniOpenid } from '@/api/account'
import { smsCode } from '@/api/app'
import { useLockFn } from '@/hooks/useLockFn'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import cache from '@/utils/cache'
import { isWeixinClient } from '@/utils/client'
import { onLoad, onShow } from '@dcloudio/uni-app'

const placeholderStyle = computed(
  () => 'color: var(---el-text-color-secondary, #7E8694);fontSize: 32rpx; fontWeight: 400'
)

// Add interfaces
interface Tenant {
  id: string | number
  name: string
  selected: boolean
  isParent?: boolean
}

interface FormData {
  scene: string
  username: string
  password: string
  code: string
  mobile: string
  openId: string
}

// Extend Uni type
// declare global {
//   interface Uni {
//     $uv: {
//       toast: (message: string) => void
//     }
//   }
// }

enum LoginTypeEnum {
  MOBILE = 'mobile',
  ACCOUNT = 'account',
}

enum LoginWayEnum {
  ACCOUNT = 1,
  MOBILE = 2,
}

const isWeixin = ref(true)
const showLoginPopup = ref(false)
// #ifdef H5
isWeixin.value = isWeixinClient()
// #endif

const userStore = useUserStore()
const appStore = useAppStore()

const websiteConfig = computed(() => appStore.getWebsiteConfig)
const uCodeRef = shallowRef()
const loginWay = ref<LoginWayEnum>(LoginWayEnum.ACCOUNT)
const codeTips = ref('')
const showPassword = ref(false)
const isCheckAgreement = ref(false)
const checkboxValue = computed(() => {
  return isCheckAgreement.value ? ['checked'] : []
})
const loginData = ref<any>({})
const formRef = ref()
const formData = reactive<FormData>({
  scene: '',
  username: '',
  password: '',
  code: '',
  mobile: '',
  openId: '',
})
const rules = reactive({
  username: [
    {
      required: true,
      message: '账号不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  password: [
    {
      required: true,
      message: '密码不能为空',
      trigger: ['blur', 'change'],
    },
  ],
})

const popupRef = ref()
const tenantList = ref<Tenant[]>([])

const showActionSheet = () => {
  popupRef.value.open()
}

const isFormValid = computed(() => {
  if (loginWay.value === LoginWayEnum.ACCOUNT) {
    return formData.username && formData.password
  } else {
    return formData.mobile && formData.code
  }
})

const handleSelectTenant = (item: Tenant) => {
  tenantList.value.forEach((tenant) => {
    if (tenant.selected) {
      tenant.selected = false
    }
  })
  item.selected = true
  cache.set('tenantId', item.id)
  popupRef.value.close()
}

const getTenantList = debounce(async (obj) => {
  const { data } = await getTenantsByLogin(obj)
  tenantList.value = data.map((item: any) => {
    return {
      ...item,
      selected: cache.getTenant() === item.id,
    }
  })
  const hasSelected = tenantList.value.some((v) => v.selected)
  const item = tenantList.value.find((v) => v.isParent)
  if (!hasSelected && item) {
    item.selected = true
    cache.set('tenantId', item.id)
  }
}, 300)

const codeChange = (text: string) => {
  codeTips.value = text
}

const sendSms = async () => {
  const { mobile } = formData
  if (!mobile || mobile.length !== 11) {
    uni.$uv.toast('手机号不合法')
    return
  }
  if (uCodeRef.value?.canGetCode) {
    const { msg } = await smsCode({
      mobile,
    })
    if (msg) {
      uni.$uv.toast(msg || '发送失败')
      return
    }
    uni.$uv.toast('发送成功')
    uCodeRef.value?.start()
  }
}

const changeLoginWay = (type: LoginTypeEnum, way: LoginWayEnum) => {
  formData.scene = type
  loginWay.value = way
  // 清空表单数据
  if (way === LoginWayEnum.ACCOUNT) {
    formData.mobile = ''
    formData.code = ''
  } else {
    formData.username = ''
    formData.password = ''
  }
  // 重置表单校验状态
  formRef.value?.resetFields()
}

/**
 * 获取微信openId
 * 1. 检查缓存中是否已有openId
 * 2. 如果没有，使用wx.login获取code
 * 3. 通过code调用getJsCode接口获取openId
 */
const getUnionId = async () => {
  try {
    // 首先检查缓存中是否已有openId
    const cachedOpenId = cache.get('wxOpenId')
    if (cachedOpenId) {
      console.log('从缓存中获取openId:', cachedOpenId)
      formData.openId = cachedOpenId
      return cachedOpenId
    }

    // #ifdef MP-WEIXIN
    console.log('缓存中没有openId，开始获取新的openId')
    const { code } = await uni.login()
    if (code) {
      console.log('获取到微信code:', code)
      const { data } = await getJsCode({
        jsCode: code,
        appId: uni.getAccountInfoSync().miniProgram?.appId || '',
      })
      if (data) {
        formData.openId = data
        console.log('获取openId成功:', data)
        // 缓存openId
        cache.set('wxOpenId', data)
        return data
      } else {
        console.log('getJsCode接口返回数据中没有openId:', data)
      }
    } else {
      console.log('wx.login获取code失败')
    }
    // #endif
    // #ifndef MP-WEIXIN
    console.log('当前环境不支持获取openId')
    // #endif
  } catch (error) {
    console.error('获取openId失败:', error)
    // 获取openId失败不影响登录流程
  }
  return null
}

const loginHandle = async (data: any) => {
  const { access_token } = data
  userStore.login(access_token)
  await userStore.getUser()
  // 用户登录成功后，保存openId到服务器
  try {
    const openId = formData.openId || cache.get('wxOpenId')
    if (openId) {
      console.log('保存openId到服务器:', openId)
      const result = await postMiniOpenid({
        openId: openId,
        username: userStore.userInfo?.sysUser?.username,
      })
      console.log('openId保存成功:', result)
    } else {
      console.log('没有openId需要保存')
    }
  } catch (error) {
    console.error('保存openId失败:', error)
  }
  uni.$uv.toast('登录成功')
  uni.hideLoading()
  uni.reLaunch({
    url: '/pages/index/index',
  })
}
const loginFun = async (scene: LoginTypeEnum) => {
  await formRef.value.validate()
  try {
    // await checkAgreement()
    if (scene == LoginTypeEnum.ACCOUNT) {
      if (!formData.username) return uni.$uv.toast('请输入账号/手机号码')
      if (!formData.password) return uni.$uv.toast('请输入密码')
    }
    if (scene == LoginTypeEnum.MOBILE) {
      if (!formData.mobile) return uni.$uv.toast('请输入手机号码')
      if (!formData.code) return uni.$uv.toast('请输入验证码')
    }
    uni.showLoading({
      title: '请稍后...',
    })
    // 在登录前尝试获取openId
    // await getUnionId()

    let data
    switch (scene) {
      case LoginTypeEnum.MOBILE:
        data = await mobileLogin(formData)
        break
      default:
        data = await accountLogin(formData)
        break
    }

    if (data) {
      loginHandle(data)
    }
  } catch (error: any) {
    uni.hideLoading()
    uni.$uv.toast(error)
  }
}
// 这是微信小程序的订阅消息模板id，需要根据实际情况修改
const supplierTmplIds: string[] = []
async function requestSubscribeMessage(scene: LoginTypeEnum): Promise<void> {
  return new Promise((resolve) => {
    if (supplierTmplIds.length) {
      wx.requestSubscribeMessage({
        tmplIds: supplierTmplIds,
        complete() {
          loginFun(scene).finally(() => resolve())
        },
        // success() {
        //   wx.getSetting({
        //     withSubscriptions: true,
        //     success: (res: any) => {
        //       // 调起授权界面弹窗
        //       const { mainSwitch, itemSettings } = res.subscriptionsSetting
        //       if (mainSwitch && itemSettings) {
        //         // 用户打开了订阅消息总开关
        //         const valid = supplierTmplIds.findIndex((item) => itemSettings[item] && itemSettings[item] !== 'accept')
        //         if (itemSettings !== null && valid === -1) {
        //           // loginFun(scene)
        //         } else {
        //           uni.$uv.toast('存在被禁用模板')
        //         }
        //       } else {
        //         uni.$uv.toast('订阅消息开关未开启')
        //       }
        //       loginFun(scene).finally(() => resolve())
        //     },
        //     fail: () => {
        //       uni.$uv.toast('操作失败')
        //       loginFun(scene).finally(() => resolve())
        //     }
        //   })
        // },
        // fail(err: any) {
        //   console.error('requestSubscribeMessage error', err)
        //   uni.$uv.toast('请先接受订单通知')
        //   loginFun(scene).finally(() => resolve())
        // }
      })
    } else {
      loginFun(scene).finally(() => resolve())
    }
  })
}

const { lockFn: handleLogin } = useLockFn(requestSubscribeMessage)

const handleCheckAgreement = (name: any) => {
  isCheckAgreement.value = name
}

const handleUpdateUser = async (value: any) => {
  await userEdit(value)
  showLoginPopup.value = false
  loginHandle(loginData.value)
}

watch(
  [() => formData.username, () => formData.mobile],
  ([username, mobile]) => {
    if (username || (mobile && mobile.length === 11)) {
      getTenantList({
        ...(username ? { username } : {}),
        ...(mobile ? { phone: mobile } : {}),
      })
    } else {
      tenantList.value = []
    }
  },
  {
    immediate: true,
  }
)

onShow(async () => {
  try {
    if (userStore.isLogin) {
      uni.showLoading({
        title: '请稍后...',
      })
      await userStore.getUser()
      uni.hideLoading()
      uni.navigateBack()
    }
  } catch (error: any) {
    uni.hideLoading()
  }
})

onLoad(async () => {
  // 清空用户信息
  userStore.logout()
  try {
    // await getUnionId()
  } catch (error) {
    console.log('预获取openId失败，登录时会重试')
  }
})
</script>

<style lang="scss">
page {
  height: 100%;
}

.login-wrapper {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-image: url('https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/ayn-login-bg.png');
  background-position: left top;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-color: linear-gradient(180deg, #f5f8ff 0%, #f2f3f5 96.15%);
  box-sizing: border-box;
  overflow: hidden;
  .logo {
    padding-top: 24vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .setting {
    top: 12vh;
    right: 32rpx;
  }
  .from {
    padding: 9vh 64rpx 12vh 64rpx;
  }
  .forget-pwd-text {
    margin-top: 24rpx;
    font-size: 28rpx;
    height: 22px;
    line-height: 22px;
  }
}

.tenant-list-scroll {
  max-height: 200px;
  height: auto;
}

.login-title {
  font-family: HYRunYuan;
  font-weight: 400;
}
.justify-text {
  /* 主对齐属性 */
  text-align: justify;
  /* 最后一行也两端对齐 */
  -webkit-text-align-last: justify;
  text-align-last: justify;
}

.icon-button-content {
  display: flex;
  width: max-content;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  text-wrap: nowrap;
  flex-shrink: 0;
  color: var(--Neutral-primary, #1c2026);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}

.login-icon-btn {
  display: flex;
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: var(---el-border-color-lighter, #f0f2f5);
  transition: all 0.15s ease-in-out;
  box-sizing: border-box;
  &:hover {
  }
  &:active {
    background-color: #e2ebf5;
    transform: scale(0.95);
  }
}
</style>
