# 登录页面 (login.vue)

## 📋 概述

登录页面是应用的用户认证入口，支持多种登录方式和租户选择功能。该页面采用 Vue 3 Composition API + TypeScript 开发，使用 uvUI 组件库和 Tailwind CSS 样式框架。

## 🎯 主要功能

### 1. 双登录方式
- **账号密码登录**: 支持用户名/手机号 + 密码登录
- **验证码登录**: 支持手机号 + 短信验证码登录

### 2. 租户选择
- 根据用户输入自动获取可用的租户列表
- 支持租户切换功能
- 租户信息本地缓存

### 3. 微信小程序集成
- 自动获取微信 openId
- 支持微信订阅消息授权
- 微信环境检测

### 4. 表单验证
- 实时表单验证
- 密码显示/隐藏切换
- 验证码倒计时功能

### 5. 用户协议
- 用户协议和隐私协议勾选
- 协议页面跳转

## 🏗️ 技术架构

### 依赖组件
```typescript
// 核心依赖
import { ref, reactive, shallowRef, watch, computed } from 'vue'
import { debounce } from 'lodash-es'

// API 接口
import { getTenantsByLogin, userEdit } from '@/api/user'
import { mobileLogin, accountLogin, getJsCode, postMiniOpenid } from '@/api/account'
import { smsCode } from '@/api/app'

// 工具函数
import { useLockFn } from '@/hooks/useLockFn'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import cache from '@/utils/cache'
import { isWeixinClient } from '@/utils/client'
```

### 数据结构

#### 表单数据 (FormData)
```typescript
interface FormData {
  scene: string        // 登录场景 (mobile/account)
  username: string     // 用户名
  password: string     // 密码
  code: string         // 验证码
  mobile: string       // 手机号
  openId: string       // 微信 openId
}
```

#### 租户数据 (Tenant)
```typescript
interface Tenant {
  id: string | number  // 租户ID
  name: string         // 租户名称
  selected: boolean    // 是否选中
  isParent?: boolean   // 是否为父级租户
}
```

#### 登录方式枚举
```typescript
enum LoginTypeEnum {
  MOBILE = 'mobile',   // 手机验证码登录
  ACCOUNT = 'account'  // 账号密码登录
}

enum LoginWayEnum {
  ACCOUNT = 1,         // 账号密码登录
  MOBILE = 2          // 手机验证码登录
}
```

## 🔧 核心功能实现

### 1. 登录方式切换
```typescript
const changeLoginWay = (type: LoginTypeEnum, way: LoginWayEnum) => {
  formData.scene = type
  loginWay.value = way
  // 清空表单数据
  if (way === LoginWayEnum.ACCOUNT) {
    formData.mobile = ''
    formData.code = ''
  } else {
    formData.username = ''
    formData.password = ''
  }
  // 重置表单校验状态
  formRef.value?.resetFields()
}
```

### 2. 微信 openId 获取
```typescript
const getUnionId = async () => {
  try {
    // 首先检查缓存中是否已有openId
    const cachedOpenId = cache.get('wxOpenId')
    if (cachedOpenId) {
      formData.openId = cachedOpenId
      return cachedOpenId
    }

    // #ifdef MP-WEIXIN
    const { code } = await uni.login()
    if (code) {
      const { data } = await getJsCode({
        jsCode: code,
        appId: uni.getAccountInfoSync().miniProgram?.appId || ''
      })
      if (data) {
        formData.openId = data
        cache.set('wxOpenId', data)
        return data
      }
    }
    // #endif
  } catch (error) {
    console.error('获取openId失败:', error)
  }
  return null
}
```

### 3. 租户列表获取
```typescript
const getTenantList = debounce(async (obj) => {
  const { data } = await getTenantsByLogin(obj)
  tenantList.value = data.map((item: any) => {
    return {
      ...item,
      selected: cache.getTenant() === item.id
    }
  })
  const hasSelected = tenantList.value.some((v) => v.selected)
  const item = tenantList.value.find((v) => v.isParent)
  if (!hasSelected && item) {
    item.selected = true
    cache.set('tenantId', item.id)
  }
}, 300)
```

### 4. 短信验证码发送
```typescript
const sendSms = async () => {
  const { mobile } = formData
  if (!mobile || mobile.length !== 11) {
    uni.$uv.toast('手机号不合法')
    return
  }
  if (uCodeRef.value?.canGetCode) {
    const { msg } = await smsCode({ mobile })
    if (msg) {
      uni.$uv.toast(msg || '发送失败')
      return
    }
    uni.$uv.toast('发送成功')
    uCodeRef.value?.start()
  }
}
```

### 5. 登录处理
```typescript
const loginFun = async (scene: LoginTypeEnum) => {
  await formRef.value.validate()
  try {
    // 表单验证
    if (scene == LoginTypeEnum.ACCOUNT) {
      if (!formData.username) return uni.$uv.toast('请输入账号/手机号码')
      if (!formData.password) return uni.$uv.toast('请输入密码')
    }
    if (scene == LoginTypeEnum.MOBILE) {
      if (!formData.mobile) return uni.$uv.toast('请输入手机号码')
      if (!formData.code) return uni.$uv.toast('请输入验证码')
    }
    
    uni.showLoading({ title: '请稍后...' })
    
    // 获取微信 openId
    await getUnionId()

    // 执行登录
    let data
    switch (scene) {
      case LoginTypeEnum.MOBILE:
        data = await mobileLogin(formData)
        break
      default:
        data = await accountLogin(formData)
        break
    }

    if (data) {
      loginHandle(data)
    }
  } catch (error: any) {
    uni.hideLoading()
    uni.$uv.toast(error)
  }
}
```

## 🎨 UI 组件

### 主要组件
- `uv-form`: 表单容器，支持验证
- `uv-input`: 输入框组件
- `uv-button`: 按钮组件
- `uv-code`: 验证码倒计时组件
- `uv-checkbox`: 复选框组件
- `uv-popup`: 弹窗组件
- `uv-list`: 列表组件
- `mplogin-popup`: 微信登录弹窗组件

### 样式特点
- 使用 Tailwind CSS 进行样式开发
- 响应式设计，适配不同屏幕尺寸
- 自定义字体和颜色主题
- 平滑的动画过渡效果

## 🔄 生命周期

### onLoad
- 清空用户信息 (userStore.logout())
- 预获取微信 openId

### onShow
- 检查用户登录状态
- 如果已登录，获取用户信息并返回上一页

## 📱 平台兼容性

### 微信小程序
- 支持微信登录
- 自动获取 openId
- 订阅消息授权

### H5
- 微信环境检测
- 适配移动端浏览器

### 其他平台
- 基础登录功能
- 租户选择功能

## 🚨 注意事项

### 1. TypeScript 类型问题
当前存在一些 TypeScript 类型错误需要修复：
- `uni.$uv` 类型定义缺失
- `wx` 对象类型定义缺失

### 2. 环境变量
需要配置以下环境变量：
- `VITE_APP_BASE_URL`: API 基础地址
- `VITE_IS_H5`: 是否为 H5 环境

### 3. 微信小程序配置
- 需要在微信小程序后台配置订阅消息模板
- 需要配置正确的 AppID

### 4. 缓存管理
- openId 缓存策略
- 租户信息缓存
- 用户登录状态缓存

## 🔗 相关文件

- `src/api/account.ts`: 登录相关 API
- `src/api/user.ts`: 用户相关 API
- `src/stores/user.ts`: 用户状态管理
- `src/stores/app.ts`: 应用状态管理
- `src/utils/cache.ts`: 缓存工具
- `src/utils/client.ts`: 客户端检测工具
- `src/hooks/useLockFn.ts`: 防重复提交 Hook

## 📝 开发建议

1. **类型安全**: 完善 TypeScript 类型定义
2. **错误处理**: 增强错误处理机制
3. **用户体验**: 优化加载状态和反馈
4. **安全性**: 加强表单验证和防重复提交
5. **可维护性**: 提取公共逻辑到 composables 