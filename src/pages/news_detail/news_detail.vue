<template>
  <view class="bg-white news-detail">
    <!-- 标题信心 -->
    <view class="news-detail-header py-[20rpx] px-[30rpx]">
      <view class="text-3xl font-medium">{{ newsData.title }}</view>
      <view class="flex mt-[20rpx] text-xs">
        <view
          class="mr-[40rpx]"
          v-if="newsData.author"
        >
          作者: {{ newsData.author }}
        </view>
        <view class="text-muted mr-[40rpx] flex-1">{{ newsData.createTime }}</view>
        <view class="flex items-center flex-none text-muted">
          <image
            src="/static/images/icon/icon_visit.png"
            class="w-[30rpx] h-[30rpx]"
          ></image>
          <view class="ml-[10rpx]">{{ newsData.visit }}</view>
        </view>
      </view>
    </view>

    <!-- 咨询内容 -->
    <view class="news-detail-section bg-white p-[24rpx]">
      <!-- 摘要 -->
      <view
        class="summary p-[20rpx] text-base"
        v-if="newsData.summary"
      >
        <text class="font-medium">摘要:</text>
        {{ newsData.summary }}
      </view>
      <!-- 内容 -->
      <view class="mt-[20rpx]">
        <uv-parse
          :html="newsData.content"
          :selectable="true"
          :lazyLoad="true"
          :domain="domain"
        ></uv-parse>
      </view>
    </view>

    <view
      class="fixed bottom-0 right-0 flex items-center mb-16 mr-4"
      @click="handleAddCollect(newsData.id)"
    >
      <uv-icon
        :name="newsData.collect ? 'star-fill' : 'star'"
        size="40"
        :color="newsData.collect ? '#F7BA47' : '#333'"
      ></uv-icon>
      <text class="ml-2">收藏</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShareAppMessage } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'
import { onLoad } from '@dcloudio/uni-app'
import { getArticleDetail, addCollect, cancelCollectByArticleId } from '@/api/news'
import { storeToRefs } from 'pinia'

const newsData = ref<any>({})
let newsId = ''
const domain = import.meta.env.VITE_APP_BASE_URL

const getData = async (id) => {
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)

  const userId = userInfo.value?.sysUser?.userId
  const { data } = await getArticleDetail({ id, userId })
  newsData.value = data
}

const handleAddCollect = async (articleId: string) => {
  try {
    if (newsData.value.collect) {
      await cancelCollectByArticleId({ articleId })
      uni.$uv.toast('已取消收藏')
    } else {
      await addCollect({ articleId })
      uni.$uv.toast('收藏成功')
    }
    getData(newsId)
  } catch (e) {
    //TODO handle the exception
  }
}

onShareAppMessage(() => {
  return {
    title: newsData.value.title,
    path: '/pages/news_detail/news_detail?id=' + newsId,
  }
})

onLoad((options: any) => {
  newsId = options.id
  getData(newsId)
})
</script>

<style lang="scss" scoped>
.news-detail {
  height: 100%;

  &-header {
    border-bottom: 2 rpx solid #f8f8f8;
  }

  &-section {
    .summary {
      border-radius: 12 rpx;
      background-color: #f7f7f7;
    }
  }
}
</style>
