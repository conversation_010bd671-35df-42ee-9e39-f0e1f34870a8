<template>
  <view class="picker">
    <view
      class="picker__wrapper"
      @click="handleArea"
    >
      <view>{{ areaText }}</view>
      <view><uv-icon name="arrow-right" /></view>
    </view>
    <pro-bottom-drawer
      ref="bottomDrawerRef"
      :config="config"
    >
      <view
        v-if="visible"
        class="picker__wrapper__content"
      >
        <pro-cascader
          v-model="areaCode"
          :options="options"
          @change="handleChange"
          @finish="onFinish"
        />
      </view>
      <!--  :lazyLoad="getArea" -->
    </pro-bottom-drawer>
  </view>
</template>
<script setup>
import { ref, computed, toRefs } from 'vue'
import ProBottomDrawer from '@/components/pro-bottom-drawer/pro-bottom-drawer.vue'
import ProCascader from '@/components/pro-cascader/pro-cascader.vue'

const emit = defineEmits(['update:modelValue', 'change'])

const props = defineProps({
  // 选中的对象
  modelValue: {
    type: Array,
    default() {
      return []
    }
  },
  options: {
    type: Array,
    default() {
      return []
    }
  },
  fieldNames: {
    type: Object,
    default() {
      return {
        textKey: 'name',
        valueKey: 'value',
        childrenKey: 'children'
      }
    }
  }
})

const { options } = toRefs(props)

const areaList = ref([])
const visible = ref(false)
const bottomDrawerRef = ref(null)
const config = ref({
  title: '所在地区',
  style: {
    height: '50%'
  }
})

const areaCode = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const areaText = computed(() => {
  return areaList.value?.map((item) => item.name).join('/') || '请选择地区'
})

const handleArea = () => {
  visible.value = true
  bottomDrawerRef.value.show()
}

const handleChange = ({ value, tabIndex, selectedOptions }) => {
  areaList.value = selectedOptions
  emit('change', { value, tabIndex, selectedOptions })
}

const onFinish = ({ selectedOptions }) => {
  areaList.value = selectedOptions
  visible.value = false
  bottomDrawerRef.value.close()
}
</script>

<style lang="scss" scoped>
.picker {
  padding: 0px 24rpx;
  &__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__wrapper__content {
    height: 50vh;
    overflow: hidden;
  }
}
</style>
