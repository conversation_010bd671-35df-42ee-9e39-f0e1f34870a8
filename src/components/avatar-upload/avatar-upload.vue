<template>
  <button
    class="p-0 m-0 rounded avatar-upload"
    :style="styles"
    hover-class="none"
    open-type="chooseAvatar"
    @click="chooseAvatar"
    @chooseavatar="chooseAvatar"
  >
    <image
      class="w-full h-full"
      mode="heightFix"
      :src="imgUrl"
      v-if="modelValue"
    />
    <slot v-else>
      <div
        :style="styles"
        class="box-border flex flex-col items-center justify-center w-full h-full text-xs border border-dotted rounded border-light text-muted"
      >
        <uv-icon
          name="plus"
          :size="36"
        />
        添加图片
      </div>
    </slot>
  </button>
</template>
<script lang="ts" setup>
import { uploadImage } from '@/api/app'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { addUnit } from '@/utils/util'
import { isBoolean } from 'lodash'
import { computed, CSSProperties, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
  },
  fileKey: {
    type: String,
    default: 'url',
  },
  size: {
    type: [String, Number],
    default: 120,
  },
  round: {
    type: [Boolean, String, Number],
    default: false,
  },
  border: {
    type: Boolean,
    default: true,
  },
})

const imgUrl = ref('')
const emit = defineEmits<{
  (event: 'update:modelValue', value: string): void
}>()
const userStore = useUserStore()
const styles = computed<CSSProperties>(() => {
  const size = addUnit(props.size)
  return {
    width: size,
    height: size,
    borderRadius: isBoolean(props.round) ? (props.round ? '50%' : '') : addUnit(props.round),
  }
})

const chooseAvatar = (e: any) => {
  // #ifndef MP-WEIXIN
  uni.navigateTo({
    url: '/pages/avatar-cropper/avatar-cropper?destWidth=300&rectWidth=200&fileType=jpg',
  })
  // #endif
  // #ifdef MP-WEIXIN
  const path = e.detail?.avatarUrl
  if (path) {
    uploadImageIng(path)
  }
  // #endif
}

const uploadImageIng = async (file: string) => {
  uni.showLoading({
    title: '正在上传中...',
  })
  try {
    const res: any = await uploadImage(file, userStore.temToken!)
    uni.hideLoading()
    imgUrl.value = getImageUrl(res.data[props.fileKey])
    emit('update:modelValue', res.data[props.fileKey])
  } catch (error) {
    uni.hideLoading()
    uni.$uv.toast(error)
  }
}

const { getImageUrl } = useAppStore()

// 监听从裁剪页发布的事件，获得裁剪结果
uni.$on('uAvatarCropper', (path) => {
  uploadImageIng(path)
})
onUnmounted(() => {
  uni.$off('uAvatarCropper')
})
</script>

<style lang="scss" scoped>
.avatar-upload {
  background: #fff;
  overflow: hidden;

  &::after {
    border: none;
  }
}
</style>
