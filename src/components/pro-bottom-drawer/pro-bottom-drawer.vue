<template>
  <uv-popup ref="popupRef" round="24rpx" mode="bottom" @change="popupChange" v-bind="$attrs">
    <view class="popup-wrapper">
      <view class="popup-header">
        <view class="header-title">
          {{ configRef.title }}
        </view>
        <image @click="handleCancel" class="cross" src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mdm/mini-customer/icon_close.png"></image>
      </view>
      <view class="popup-content">
        <view class="content-style" v-if="configRef.content" nodes="{{configRef.content}}"></view>
        <slot v-else></slot>
      </view>
      <view class="popup-footer" v-if="!$slots.footer">
        <pro-button v-if="configRef.showCancelBtn" size="large" @tap="handleCancel" :plain="true" :hairline="true" :text="configRef.cancelBtnText"></pro-button>
        <pro-button v-if="configRef.showConfirmBtn" size="large" :disabled="configRef.confirmDisabled" @tap="handleConfirm" type="primary" :text="configRef.confirmBtnText"></pro-button>
      </view>
      <template v-if="$slots.footer">
        <slot name="footer"></slot>
      </template>
    </view>
  </uv-popup>
</template>
<script setup>
import { ref, toRefs, computed } from 'vue';
const props = defineProps({
  config: {
    type: Object,
    default: () => {
      return {}
    }
  },
})

const { config } = toRefs(props)

const configRef = computed(()=>{
  return {
    cancelBtnText:'取消',
    confirmBtnText:'确定',
    showCancelBtn:false,
    showConfirmBtn:true,
    confirmDisabled:false,
    confirmWithClose: true,
    title:'标题',
    ...config.value
  }
})

const popupRef = ref(null)

const emits = defineEmits(['onCancel', 'onConfirm', 'onClose'])

function show(){
  popupRef.value.open()
}

function close(){
  popupRef.value.close()
}

function handleCancel(){
  emits('onCancel')
  close()
}

function handleConfirm(){
  if(configRef?.value?.confirmDisabled){
    return
  }
  if(configRef.value.confirmWithClose){
    close()
  }
  emits('onConfirm')
}

function popupChange(e){
  if(e.show === false){
    emits('onClose')
  }
}

defineExpose({
  show,
  close
})
</script>
<style lang="scss" scoped>
.popup-wrapper {
  width: 100%;
  box-sizing: border-box;
  max-height: 70vh;
  overflow-y: auto;
  padding: 32rpx 32rpx 16rpx 32rpx;
  .popup-header {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      line-height: 52rpx;
      color: rgba(0, 0, 0, 0.90);
      text-align: center;
    }
    .cross {
      position: absolute;
      right: 0rpx;
      top: 0rpx;
      width: 48rpx;
      height: 48rpx;
    }
  }

  .popup-content {
    margin: 16rpx 0 24rpx 0;
    .content-style {
      font-size: 32rpx;
      font-weight: 400;
      color: #1C2026;
      line-height: 48rpx;
    }
  }

  .popup-footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24rpx;
  }
}



</style>
