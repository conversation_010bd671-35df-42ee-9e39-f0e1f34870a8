<template>
  <view class="pro-radio-group-wrapper">
    <view
      class="radio-group-item"
      v-for="item in config"
      :key="item.value"
      @tap.stop="handleTap(item.value)"
    >
      <image
        v-if="item.value === modelValue"
        class="radio-group-item-label-icon"
        src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-checked.png"
      ></image>
      <image
        v-if="item.value !== modelValue"
        class="radio-group-item-label-icon"
        src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/todo/checkbox-unchecked.png"
      ></image>
      <view class="radio-group-item-label">
        {{ item.label }}
      </view>
    </view>
  </view>
</template>
<script setup>
// import { ref, toRefs, computed } from "vue";

const props = defineProps({
  config: {
    type: Object,
    default: () => {
      return {}
    }
  },
  modelValue: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue'])

function handleTap(value) {
  if (value !== props.modelValue) {
    emits('update:modelValue', value)
  }
}

defineExpose({})
</script>
<style lang="scss" scoped>
.pro-radio-group-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 48rpx;
  .radio-group-item {
    display: flex;
    align-items: center;
    justify-content: center;
    .radio-group-item-label {
      width: max-content;
      color: #000;
      text-align: left;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
    }
    .radio-group-item-label-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
      flex-shrink: 0;
    }
  }
}
</style>
