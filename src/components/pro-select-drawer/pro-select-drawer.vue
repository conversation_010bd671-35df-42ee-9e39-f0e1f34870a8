<template>
    <pro-bottom-drawer 
      ref="drawer"
      :config="{title: title || '请选择'}"
      @onConfirm="onConfirm"
      @onCancel="onCancel"
    >
      <!-- 搜索框 -->
      <view v-if="showSearch" class="pro-select__search" @touchmove.prevent>
        <uv-search
          v-model="searchKeyword"
          :placeholder="placeholder"
          shape="round"
          :showAction="false"
          @search="handleSearch"
          @clear="handleSearchClear"
        ></uv-search>
      </view>
      
      <scroll-view 
        class="pro-select__scroll" 
        :scroll-y="true"
        @scrolltolower="onScrollToLower"
        :style="{height: scrollHeight}"
      >
        <view class="pro-select__list">
          <view 
            v-for="(item, index) in displayData" 
            :key="index" 
            class="pro-select__item"
            :class="{ 'pro-select__item--active': isItemSelected(item) }"
            @tap="selectItem(item)"
          >
            <text class="pro-select__label">{{ getItemLabel(item) }}</text>
            <uv-icon v-if="isItemSelected(item)" name="checkmark" color="#0F8B3B" size="16"></uv-icon>
          </view>
          
          <!-- 加载状态 -->
          <view v-if="loading" class="pro-select__loading">
            <uv-loading-icon></uv-loading-icon>
            <text class="loading-text">加载中...</text>
          </view>
          
          <!-- 无数据状态 -->
          <view v-if="!loading && displayData.length === 0" class="pro-select__empty">
            <uv-empty mode="data" text="暂无数据"></uv-empty>
          </view>
        </view>
      </scroll-view>
    </pro-bottom-drawer>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'

const props = defineProps({
  // 选项数据
  options: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 占位符
  placeholder: {
    type: String,
    default: '输入关键字搜索'
  },
  // 选中的值
  modelValue: {
    type: [String, Number, Object, Array],
    default: null
  },
  // 自定义label字段名
  labelField: {
    type: String,
    default: 'label'
  },
  // 自定义value字段名
  valueField: {
    type: String,
    default: 'value'
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: false
  },
  // API函数，用于获取数据
  apiFn: {
    type: Function,
    default: null
  },
  // 附加参数，会在请求时传递给接口
  params: {
    type: Object,
    default: () => ({})
  },
  // 是否使用本地分页
  useLocalPage: {
    type: Boolean,
    default: false
  },
  // 滚动区域高度
  scrollHeight: {
    type: String,
    default: '40vh'
  },
  // 搜索关键字参数名，用于自定义搜索关键字在API请求中的字段名
  keywordField: {
    type: String,
    default: 'keyword'
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const drawer = ref(null)
const selectedItems = ref([]) // 改为数组，支持多选
const searchKeyword = ref('')
const loading = ref(false)
const localData = ref([])
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 处理数据显示
const displayData = computed(() => {
  // 如果没有启用本地分页，直接返回localData
  if (!props.useLocalPage) {
    return localData.value
  }
  
  // 本地分页和搜索处理
  let filteredData = props.apiFn ? localData.value : props.options
  
  // 搜索过滤
  if (searchKeyword.value && props.showSearch) {
    filteredData = filteredData.filter(item => {
      const label = getItemLabel(item)
      // 添加空值检查，防止 toString 方法报错
      return label != null ? label.toString().toLowerCase().includes(searchKeyword.value.toLowerCase()) : false
    })
  }
  
  // 本地分页处理
  if (props.useLocalPage) {
    const start = (pagination.value.pageNum - 1) * pagination.value.pageSize
    const end = start + pagination.value.pageSize
    return filteredData.slice(0, end) // 返回从0到当前页的所有数据
  }
  
  return filteredData
})

// 获取选项的标签
const getItemLabel = (item) => {
  if (!item) return ''
  if (typeof item === 'string') return item
  return item[props.labelField] || ''
}

// 获取选项的值
const getItemValue = (item) => {
  if (!item) return ''
  if (typeof item === 'string') return item
  return item[props.valueField] || ''
}

// 判断选项是否被选中
const isItemSelected = (item) => {
  if (!selectedItems.value.length) return false
  
  if (props.multiple) {
    // 多选模式
    return selectedItems.value.some(selectedItem => {
      if (typeof item === 'string' && typeof selectedItem === 'string') {
        return item === selectedItem
      }
      return getItemValue(item) === getItemValue(selectedItem)
    })
  } else {
    // 单选模式
    if (typeof item === 'string' && typeof selectedItems.value[0] === 'string') {
      return item === selectedItems.value[0]
    }
    return getItemValue(item) === getItemValue(selectedItems.value[0])
  }
}

// 根据值找到对应的选项
const setSelectedItemsByValue = (value) => {
  if (!value) {
    selectedItems.value = []
    return
  }

  if (props.multiple) {
    // 多选模式
    if (!Array.isArray(value)) {
      value = [value]
    }
    selectedItems.value = value.map(val => {
      if (typeof val === 'object') {
        return val
      }
      return findItemByValue(val)
    }).filter(Boolean)
  } else {
    // 单选模式
    if (typeof value === 'object') {
      selectedItems.value = [value]
      return
    }
    const item = findItemByValue(value)
    selectedItems.value = item ? [item] : []
  }
}

// 根据值查找选项
const findItemByValue = (value) => {
  const findInData = (data) => {
    return data.find(item => getItemValue(item) === value)
  }
  
  return findInData(props.apiFn ? localData.value : props.options)
}

// 监听modelValue变化
watch([() => props.modelValue, () => props.options], ([newVal, options]) => {
  if (newVal && options && options.length > 0) {
    setSelectedItemsByValue(newVal)
  }
}, { immediate: true })

// 监听options变化
watch(() => props.options, (newVal) => {
  if (!props.apiFn) {
    localData.value = newVal
    // 如果modelValue有值，重新设置选中项
    if (props.modelValue && newVal && newVal.length > 0) {
      setSelectedItemsByValue(props.modelValue)
    }
  }
})

// 加载数据
const loadData = async (reset = false) => {
  if (!props.apiFn && !props.useLocalPage) return
  
  try {
    loading.value = true
    
    if (reset) {
      pagination.value.pageNum = 1
      localData.value = []
    }
    
    if (props.apiFn) {
      // 使用API获取数据
      const params = {
        ...pagination.value,
        ...props.params // 添加用户传入的附加参数
      }
      
      // 添加搜索关键字，使用自定义字段名
      if (searchKeyword.value) {
        params[props.keywordField] = searchKeyword.value
      }
      
      const res = await props.apiFn(params)
      
      // 处理数据，预期返回格式为 res.data.records
      if (res && res.data) {
        if (reset) {
          localData.value = res.data.records || []
        } else {
          localData.value = [...localData.value, ...(res.data.records || [])]
        }
        
        pagination.value.total = res.data.total || 0
      }
    } else if (props.useLocalPage) {
      // 本地数据分页处理
      localData.value = props.options
    }
  } catch (error) {
    console.error('加载数据失败', error)
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.value.pageNum = 1 // 重置分页
  if (props.apiFn) {
    loadData(true)
  }
}

// 清除搜索
const handleSearchClear = () => {
  searchKeyword.value = ''
  pagination.value.pageNum = 1 // 重置分页
  if (props.apiFn) {
    loadData(true)
  }
}

// 初始化
onMounted(() => {
  if (props.apiFn) {
    loadData()
  } else {
    localData.value = props.options
  }
})

// 打开底部抽屉
const show = () => {
  // 重置搜索和分页，重新加载数据
  if (props.showSearch) {
    searchKeyword.value = ''
  }
  
  // 重置分页
  pagination.value.pageNum = 1
  
  if (props.apiFn || props.useLocalPage) {
    loadData(true)
  }
  
  drawer.value.show()
}

// 选择项目
const selectItem = (item) => {
  if (props.multiple) {
    // 多选模式
    const index = selectedItems.value.findIndex(selectedItem => {
      if (typeof item === 'string' && typeof selectedItem === 'string') {
        return item === selectedItem
      }
      return getItemValue(item) === getItemValue(selectedItem)
    })
    
    if (index === -1) {
      selectedItems.value.push(item)
    } else {
      selectedItems.value.splice(index, 1)
    }
  } else {
    // 单选模式
    selectedItems.value = [item]
  }
}

// 确认选择
const onConfirm = () => {
  if (props.multiple) {
    // 多选模式
    const values = selectedItems.value.map(item => getItemValue(item))
    emit('update:modelValue', values)
    emit('change', {
      value: values,
      items: selectedItems.value
    })
  } else {
    // 单选模式
    const value = selectedItems.value[0] ? getItemValue(selectedItems.value[0]) : null
    emit('update:modelValue', value)
    emit('change', {
      value,
      item: selectedItems.value[0]
    })
  }
}

// 取消选择
const onCancel = () => {
  // 不做处理，保持原来的选择
}

// 滚动到底部触发加载更多
const onScrollToLower = () => {
  if (loading.value) return
  loadMore()
}

// 加载更多
const loadMore = () => {
  if (loading.value) return
  
  if (props.apiFn) {
    // API分页
    if (localData.value.length < pagination.value.total) {
      pagination.value.pageNum += 1
      loadData()
    }
  } else if (props.useLocalPage) {
    // 本地分页
    const filteredOptions = props.options.filter(item => {
      if (!searchKeyword.value || !props.showSearch) return true
      const label = getItemLabel(item)
      // 添加空值检查，防止 toString 方法报错
      return label != null ? label.toString().toLowerCase().includes(searchKeyword.value.toLowerCase()) : false
    })
    
    const currentTotal = (pagination.value.pageNum * pagination.value.pageSize)
    if (currentTotal < filteredOptions.length) {
      pagination.value.pageNum += 1
    }
  }
}

defineExpose({
  show
})
</script>

<style lang="scss">
.pro-select__search {
  margin-bottom: 8px;
}

.pro-select__scroll {
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度 */
}

.pro-select__list {
  padding: 8px 0;
}

.pro-select__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.pro-select__label {
  font-size: 14px;
}

.pro-select__item--active {
  background-color: transparent;
}

.pro-select__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  
  .loading-text {
    margin-left: 8px;
    font-size: 14px;
    color: #909399;
  }
}

.pro-select__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}
</style>
