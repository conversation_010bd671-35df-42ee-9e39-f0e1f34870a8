<template>
  <view class="cascader">
    <view class="cascader__header">
      <uv-tabs
        :list="tabs"
        :current="activeTabIndex"
        :lineColor="activeColor"
        :activeStyle="{
          color: activeColor,
          fontWeight: 'bold',
          transform: 'scale(1.05)',
        }"
        :inactiveStyle="{
          color: inactiveColor,
          transform: 'scale(1)',
        }"
        @click="tabClick"
      ></uv-tabs>
    </view>
    <view class="cascader__content">
      <view class="cascader__content__wrap">
        <view
          :class="['cascader__content__tabcontent', { active: tabIndex === activeTabIndex }]"
          class=""
          :key="tabIndex"
          v-for="(tab, tabIndex) in tabList"
        >
          <scroll-view
            class="cascader__content__scroll"
            :scroll-y="true"
          >
            <view
              :key="index"
              v-for="(item, index) in tab?.options"
              :class="['cascader__content__item', { active: tab?.selected?.[valueKey] === item?.[valueKey] }]"
              @click="() => clickTabItem(item, tabIndex)"
            >
              <view
                :style="
                  tab?.selected?.[valueKey] === item?.[valueKey]
                    ? {
                        color: props.activeColor,
                      }
                    : {}
                "
              >
                {{ item.name }}
              </view>
              <view v-if="tab?.selected?.[valueKey] === item?.[valueKey]">
                <uv-icon
                  name="checkmark"
                  :color="props.activeColor"
                />
              </view>
            </view>
          </scroll-view>
          <!-- 加载状态 -->
          <view
            v-if="loading"
            class="cascader_loading"
          >
            <uv-loading-icon></uv-loading-icon>
            <text class="loading-text">加载中...</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, toRefs, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

const emit = defineEmits(['update:modelValue', 'change', 'finish'])

const props = defineProps({
  // 选中的对象
  modelValue: {
    type: Array,
    default() {
      return []
    },
  },
  inactiveColor: {
    type: String,
    default: '#606266',
  },
  activeColor: {
    type: String,
    default: '#27a93f',
  },
  // 加载数据的方法
  lazyLoad: {
    type: Function,
    default() {
      return () => {}
    },
  },
  options: {
    type: Array,
    default() {
      return []
    },
  },
  lazy: {
    type: Boolean,
    default: false,
  },
  fieldNames: {
    type: Object,
    default() {
      return {
        textKey: 'name',
        valueKey: 'value',
        childrenKey: 'children',
      }
    },
  },
})

const { lazy, options, fieldNames, activeColor, inactiveColor } = toRefs(props)
const { textKey, valueKey, childrenKey } = fieldNames.value
const tabList = ref([])
const activeTabIndex = ref(0)
const loading = ref(false)

const list = ref([])

const tabs = computed({
  get() {
    const _tabs = tabList.value.map((item) => {
      return {
        name: item?.selected?.[textKey] || '请选择',
        value: item?.selected?.[valueKey] || '',
        ...item,
      }
    })
    return [..._tabs]
  },
  set(val) {
    return val
  },
})
const tabClick = ({ index }) => {
  nextTick(() => {
    activeTabIndex.value = index
  })
}

const data = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  },
})

const getSelectedOptionsByValue = (options, value) => {
  for (const option of options) {
    if (option[valueKey] === value) {
      return [option]
    }

    if (option[childrenKey]) {
      const selectedOptions = getSelectedOptionsByValue(option[childrenKey], value)
      if (selectedOptions) {
        return [option, ...selectedOptions]
      }
    }
  }
}

const updateTabs = () => {
  const { options, modelValue } = props

  if (modelValue !== undefined) {
    const selectedOptions = getSelectedOptionsByValue(options, modelValue)

    if (selectedOptions) {
      let optionsCursor = options

      tabList.value = selectedOptions.map((option) => {
        const tab = {
          options: optionsCursor,
          selected: option,
        }

        const next = optionsCursor.find((item) => item[valueKey] === option[valueKey])
        if (next) {
          optionsCursor = next[childrenKey]
        }

        return tab
      })

      if (optionsCursor) {
        tabList.value.push({
          options: optionsCursor,
          selected: null,
        })
      }

      nextTick(() => {
        activeTabIndex.value = tabList.value.length - 1
      })

      return
    }
  }

  tabs.value = [
    {
      options,
      selected: null,
    },
  ]
}

watch(() => props.options, updateTabs, { deep: true })

watch(
  () => props.modelValue,
  (value) => {
    if (value !== undefined) {
      const values = tabList.value.map((tab) => tab.selected?.[valueKey])
      if (values.includes(value)) {
        return
      }
    }
    updateTabs()
  }
)

const clickTabItem = (option, tabIndex) => {
  if (option?.disabled) {
    return
  }

  tabList.value[tabIndex].selected = option

  if (tabList.value.length > tabIndex + 1) {
    tabList.value = tabList.value.slice(0, tabIndex + 1)
  }

  if (lazy.value) {
    if (loading.value) return
    loadChildren({ columnIndex: tabs.value.length, item, index })
  } else {
    if (option[childrenKey]) {
      const nextTab = {
        options: option[childrenKey],
        selected: null,
      }

      if (tabList.value[tabIndex + 1]) {
        tabList.value[tabIndex + 1] = nextTab
      } else {
        tabList.value.push(nextTab)
      }

      nextTick(() => {
        activeTabIndex.value++
      })
    }
  }

  const selectedOptions = tabList.value.map((tab) => tab.selected).filter(Boolean)
  data.value = option[valueKey]

  const params = {
    value: option[valueKey],
    tabIndex,
    selectedOptions,
  }
  emit('change', params)
  if (!option[childrenKey]) {
    emit('finish', params)
  }
}

const loadChildren = (node) => {
  loading.value = true
  try {
    props.lazyLoad(node, (children) => {
      list.value = children
      loading.value = false
    })
  } catch (error) {
    loading.value = false
    console.log('error', error)
  }
}

onMounted(() => {
  tabList.value = []
  if (options.value.length) {
    tabList.value.push({
      options: options.value,
      selected: null,
    })
  }
  if (lazy.value) {
    loadChildren(undefined)
  }

  updateTabs()

  nextTick(() => {
    activeTabIndex.value = tabList.value.length ? tabList.value.length - 1 : 0
  })
})

onUnmounted(() => {
  tabList.value = []
  activeTabIndex.value = 0
  options.value = []
})
</script>
<style scoped lang="scss">
.cascader {
  height: 100%;
  &__header {
    width: 100%;
  }
  &__content {
    height: calc(100% - 88rpx);
    padding: 16rpx 0rpx;
    min-height: 600rpx;
  }
  &__content__tabcontent,
  &__content__wrap {
    height: 100%;
  }

  &__content__wrap {
    position: relative;
  }

  &__content__tabcontent {
    position: absolute;
    width: 100vw;
    display: none;
    &.active {
      display: block;
    }
  }
  &__loading {
    .loading-text {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100rpx;
    }
  }

  &__content__item {
    padding: 8rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.active {
      font-weight: 500;
    }
  }
  &__content__scroll {
    height: 100%;
  }
}
</style>
