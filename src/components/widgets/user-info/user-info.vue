<template>
  <view class="user-info flex px-[50rpx] justify-between py-[50rpx]">
    <view
      v-if="isLogin"
      class="flex items-center"
      @click="navigateTo('/pages/user_data/user_data')"
    >
      <uv-avatar
        :src="getImageUrl(user.sysUser.avatar)"
        :size="120"
      ></uv-avatar>
      <view class="text-white ml-[20rpx]">
        <view class="text-2xl">{{ user.sysUser.nickname }}</view>
        <view
          class="text-xs mt-[18rpx]"
          @click.stop="copy(user.sysUser.username)"
        >
          账号：{{ user.sysUser.phone ? user.sysUser.username : '请绑定手机' }}
        </view>
      </view>
    </view>
    <navigator
      v-else
      class="flex items-center"
      hover-class="none"
      url="/pages/login/login"
    >
      <uv-avatar
        src="/static/images/user/default_avatar.png"
        :size="120"
      ></uv-avatar>
      <view class="text-white text-3xl ml-[20rpx]">未登录</view>
    </navigator>
    <navigator
      v-if="isLogin"
      hover-class="none"
      url="/pages/user_set/user_set"
    >
      <uv-icon
        name="setting"
        color="#fff"
        :size="48"
      ></uv-icon>
    </navigator>
  </view>
</template>
<script lang="ts" setup>
import { useCopy } from '@/hooks/useCopy'
import { useAppStore } from '@/stores/app'

defineProps({
  content: {
    type: Object,
    default: () => ({}),
  },
  styles: {
    type: Object,
    default: () => ({}),
  },
  user: {
    type: Object,
    default: () => ({}),
  },
  isLogin: {
    type: Boolean,
  },
})
const { copy } = useCopy()
const navigateTo = (url: string) => {
  uni.navigateTo({
    url,
  })
}
const { getImageUrl } = useAppStore()
</script>

<style lang="scss" scoped>
.user-info {
  background: url('../../../static/images/user/my_topbg.png');
  height: 115px;
  background-position: bottom;
  background-size: 100% auto;
}
</style>
