<template>
  <view class="pro-time-axis">
    <slot />
  </view>
</template>

<script>
/**
 * timeLine 时间轴
 * @description 时间轴组件一般用于物流信息展示，各种跟时间相关的记录等场景。
 * @example <pro-time-line></pro-time-line>
 */
export default {
  name: 'pro-time-line',
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.pro-time-axis {
  padding-left: 40rpx;
  position: relative;
}

.pro-time-axis::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 12rpx;
  width: 1px;
  bottom: 0;
  border-left: 1px solid #ddd;
  transform-origin: 0 0;
  transform: scaleX(0.5);
}
</style>
