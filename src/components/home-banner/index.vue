<template>
  <view class="home-banner-wrapper">
    <view class="home-banner-content">
      <!-- 这里可以放置轮播图或者静态Banner -->
      <view class="banner-placeholder">
        <text class="banner-text">爱养牛招采平台</text>
        <text class="banner-subtitle">专业的采购管理平台</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// Banner组件逻辑
</script>

<style lang="scss" scoped>
.home-banner-wrapper {
  width: 100%;
  height: 100px;
  border-radius: 6px;
  overflow: hidden;
}

.home-banner-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.banner-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.banner-text {
  color: #FFFFFF;
  font-family: 'PingFang SC';
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

.banner-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
</style>
