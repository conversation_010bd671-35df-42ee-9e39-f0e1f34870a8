<template>
  <view class="pro-bottom-button-wrapper">
    <view class="wrapper pure-button-group-border">
      <uv-button
        v-if="configRef.showCancelBtn"
        size="large"
        @click.stop="handleCancel"
        :disabled="configRef.cancelDisabled"
        :plain="true"
        :hairline="true"
        type="primary"
        color="#2C6AE3"
        shape="circle"
        style="flex: 1"
        :customStyle="{
          borderColor: 'rgba(44, 106, 227, 0.20) !important',
          borderWidth: '2rpx !important',
          borderRadius: '64rpx !important'
        }"
      >
        {{ configRef.cancelBtnText }}
      </uv-button>
      <uv-button
        v-if="configRef.showConfirmBtn"
        size="large"
        :disabled="configRef.confirmDisabled"
        @click.stop="handleConfirm"
        type="primary"
        color="#2C6AE3"
        shape="circle"
        style="flex: 1"
        :customStyle="{
          borderRadius: '64rpx !important'
        }"
      >
        {{ configRef.confirmBtnText }}
      </uv-button>
    </view>
  </view>
</template>
<script setup>
import { toRefs, computed } from 'vue'

const props = defineProps({
  config: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const { config } = toRefs(props)

const configRef = computed(() => {
  return {
    cancelBtnText: '取消',
    confirmBtnText: '确定',
    showCancelBtn: true,
    showConfirmBtn: true,
    confirmDisabled: false,
    cancelDisabled: false,
    ...config.value
  }
})

const emits = defineEmits(['onCancel', 'onConfirm'])

function handleConfirm() {
  if (configRef?.value?.confirmDisabled) {
    return
  }
  emits('onConfirm')
}
function handleCancel() {
  if (configRef?.value?.cancelDisabled) {
    return
  }
  emits('onCancel')
}
defineExpose({})
</script>
<style lang="scss" scoped>
.pro-bottom-button-wrapper {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
  .wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 32rpx;
    padding: 24rpx 32rpx;
  }
}
</style>
