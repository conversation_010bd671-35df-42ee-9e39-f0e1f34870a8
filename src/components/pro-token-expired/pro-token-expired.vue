<template>
  <view class="token-expired-wrapper">
    <uv-empty :text="text">
      <uv-button
        type="primary"
        shape="circle"
        @click="handleLogin"
      >
        {{ title }}
      </uv-button>
    </uv-empty>
  </view>
</template>
<script setup>
defineProps({
  text: {
    type: String,
    default: () => {
      return '登录已过期，请重新登录'
    }
  },
  title: {
    type: String,
    default: () => {
      return '点击登录'
    }
  }
})

function handleLogin() {
  uni.reLaunch({
    url: '/pages/login/login'
  })
}
</script>
<style lang="scss" scoped>
.token-expired-wrapper {
  padding-top: 30vh;
}
</style>
