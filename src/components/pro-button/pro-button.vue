<template>
  <uv-button
      v-if="$slots.default"
    class="pro-button-wrapper"
    :type="type"
    :size="size"
    :text="text"
    :custom-style="{
			...computedBorderRadius,
			width: '100%',
    }"
		:style="dynamicStyle"
    v-bind="$attrs"
    @getphonenumber="getphonenumber"
  >
    <image class="icon" v-if="iconSrc" :src="iconSrc"> </image>
    <slot v-if="$slots.default" />
  </uv-button>
  <uv-button
      v-else
      class="pro-button-wrapper"
      :type="type"
      :size="size"
      :text="text"
      :custom-style="{
			...computedBorderRadius,
			width: '100%',
    }"
      :style="dynamicStyle"
      v-bind="$attrs"
      @getphonenumber="getphonenumber"
  >
  </uv-button>
</template>
<script setup>
import { ref, computed, useAttrs, toRefs } from 'vue'

const attrs = useAttrs();

const ps = defineProps({
  text: {
    type: String,
    default: "按钮",
  },
  type: {
    type: String,
    default: "primary",
  },
  size: {
    type: String,
    default: "normal",
  },
	width: {
	  type: String,
	  default: "",
	},
	borderRadius: {
	  type: String,
	  default: "16rpx",
	},
  iconSrc: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(['getphonenumber']);

function getphonenumber(e){
  emits('getphonenumber', e)
}

const { width, borderRadius } = toRefs(ps);
// 计算属性动态生成样式
const dynamicStyle = computed(() => ({
  '--dynamic-width': width.value ? width.value : 'auto',
  '--dynamic-flex': width.value ? 'unset' : 1,
	width: width.value ? width.value : 'auto',
	flex: width.value ? 'unset' : 1
}));
const computedBorderRadius = computed(() => {
	if (attrs?.shape !== 'circle' && borderRadius.value) {
    return { borderRadius: borderRadius.value };
	}
	return {};
})

</script>
<script>
export default {
  options: {
    multipleSlots: true,
    virtualHost: true,
		styleIsolation: "shared"
  },
};
</script>

<style scoped lang="scss">
.pro-button-wrapper {
	 // width: var(--dynamic-width, 'auto');
	 // flex: var(--dynamic-flex, 1);
  .icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 8rpx;
  }
}
</style>
