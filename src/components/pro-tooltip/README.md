# ProTooltip 文本省略提示组件

一个功能强大的文本省略组件，支持单行/多行省略，并在文本被省略时提供点击展示完整内容的 tooltip 功能。

## ✨ 特性

- 📝 **智能省略**: 支持单行和多行文本省略（1-10行可配置）
- 🎯 **智能检测**: 自动检测文本是否被省略，短文本和未省略文本不会响应点击
- 💡 **切换显示**: 点击省略文本显示完整内容，再次点击隐藏，提供完整的交互体验
- 🎨 **高度可定制**: 支持自定义字体、颜色、tooltip 样式等
- 📱 **移动端优化**: 专为移动端设计，支持触摸交互
- 🔧 **边界处理**: 智能处理屏幕边界，确保 tooltip 始终可见
- 🚀 **性能优化**: 使用高效的检测算法，避免不必要的计算
- 🌍 **跨平台兼容**: 支持 H5、小程序、APP 等多端

## 📦 安装使用

```vue
<template>
  <!-- 长文本会被省略，点击可切换显示/隐藏完整内容 -->
  <pro-tooltip 
    text="这是一段很长的文本，会被省略，点击可以显示完整内容，再次点击可以隐藏..."
    :lines="2"
    @click="handleClick"
    @tooltip-show="handleShow"
    @tooltip-hide="handleHide"
  />
  
  <!-- 短文本不会被省略，点击不会显示tooltip -->
  <pro-tooltip 
    text="短文本"
    :lines="1"
  />
</template>

<script setup>
import ProTooltip from '@/components/pro-tooltip/pro-tooltip.vue'

const handleClick = (text) => {
  console.log('点击了文本:', text)
}

const handleShow = (text) => {
  console.log('显示完整内容')
}

const handleHide = (text) => {
  console.log('隐藏完整内容')
}
</script>
```

## 📚 API 文档

### Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `text` | `String` | `''` | 要显示的文本内容 **必填** |
| `lines` | `Number` | `1` | 最大显示行数（1-10） |
| `disabled` | `Boolean` | `false` | 是否禁用点击功能 |
| `clickable` | `Boolean` | `true` | 是否允许点击 |
| `maxWidth` | `String\|Number` | `'100%'` | 文本容器最大宽度 |
| `fontSize` | `String\|Number` | `'14px'` | 字体大小 |
| `lineHeight` | `String\|Number` | `'1.4'` | 行高 |
| `color` | `String` | `'#333'` | 文字颜色 |
| `tooltipBgColor` | `String` | `'rgba(0, 0, 0, 0.8)'` | tooltip 背景色 |
| `tooltipTextColor` | `String` | `'#fff'` | tooltip 文字颜色 |
| `tooltipMaxWidth` | `String\|Number` | `'300px'` | tooltip 最大宽度 |
| `minCheckLength` | `Number` | `10` | 最小检测长度，短于此长度的文本不检测省略 |
| `useAdvancedDetection` | `Boolean` | `true` | 是否使用高级检测算法（更精确但稍复杂） |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `(text: String)` | 点击文本时触发（无论是否省略） |
| `tooltip-show` | `(text: String)` | tooltip 显示时触发 |
| `tooltip-hide` | `(text: String)` | tooltip 隐藏时触发 |

### Methods 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `checkEllipsis` | - | - | 手动检查文本省略状态 |
| `hideTooltip` | - | - | 手动隐藏 tooltip |
| `isEllipsis` | - | `Boolean` | 获取当前是否处于省略状态 |

## 💫 核心交互特性

### 智能检测与响应
- ✅ **长文本被省略** → 点击显示完整内容，再次点击隐藏
- ❌ **短文本未省略** → 点击无响应，避免不必要的交互
- ✅ **多行文本超出** → 智能检测高度变化
- ❌ **多行文本未超出** → 不显示省略效果

### 交互流程
1. **加载时**: 自动检测文本是否被省略
2. **省略状态**: 显示省略号(`...`)，文本可点击
3. **点击显示**: 在点击位置弹出tooltip显示完整内容
4. **再次点击**: 隐藏tooltip，回到省略状态
5. **点击遮罩**: 也可以隐藏tooltip

## 🎯 使用示例

### 基础用法

```vue
<template>
  <!-- 单行省略 - 长文本会被省略，点击切换显示/隐藏 -->
  <pro-tooltip 
    text="这是一段很长的文本，在单行模式下会被省略，点击可以查看完整内容"
    :lines="1"
  />
  
  <!-- 多行省略 - 超过3行会显示省略号 -->
  <pro-tooltip 
    text="这是一段很长的文本内容，当超过指定行数时会显示省略号，点击省略的文本可以看到完整内容，再次点击可以隐藏"
    :lines="3"
  />
  
  <!-- 短文本 - 不会被省略，点击无响应 -->
  <pro-tooltip 
    text="短文本"
    :lines="1"
  />
</template>
```

### 自定义样式

```vue
<template>
  <pro-tooltip 
    text="自定义样式的文本内容"
    :lines="2"
    color="#1890ff"
    fontSize="16px"
    lineHeight="1.6"
    maxWidth="200px"
    tooltipBgColor="#1890ff"
    tooltipTextColor="#fff"
  />
</template>
```

### 检测算法配置

```vue
<template>
  <!-- 使用高级检测算法（默认），适合混合文本 -->
  <pro-tooltip 
    text="This is 中英文混合的文本内容，包含数字123和标点符号!"
    :lines="1"
    :useAdvancedDetection="true"
    :minCheckLength="15"
  />
  
  <!-- 使用简单检测算法，性能更好但精度略低 -->
  <pro-tooltip 
    text="纯中文文本内容适合使用简单检测算法"
    :lines="1"
    :useAdvancedDetection="false"
    :minCheckLength="8"
  />
</template>
```

### 交互事件处理

```vue
<template>
  <pro-tooltip 
    text="这是一段支持交互的长文本，点击可以切换显示和隐藏完整内容，支持完整的事件监听"
    :lines="2"
    @click="handleClick"
    @tooltip-show="handleShow"
    @tooltip-hide="handleHide"
  />
</template>

<script setup>
const handleClick = (text) => {
  // 每次点击都会触发，无论是否显示tooltip
  console.log('用户点击了文本:', text)
}

const handleShow = (text) => {
  // 只有在tooltip显示时才触发
  console.log('显示完整内容:', text)
}

const handleHide = (text) => {
  // 只有在tooltip隐藏时才触发
  console.log('隐藏完整内容')
}
</script>
```

### 禁用状态

```vue
<template>
  <!-- 禁用点击 -->
  <pro-tooltip 
    text="这段文本禁用了点击功能"
    :lines="1"
    :disabled="true"
  />
  
  <!-- 不可点击 -->
  <pro-tooltip 
    text="这段文本设置为不可点击"
    :lines="1"
    :clickable="false"
  />
</template>
```

### 响应式文本

```vue
<template>
  <pro-tooltip 
    :text="dynamicText"
    :lines="2"
    ref="tooltipRef"
  />
  <button @click="changeText">改变文本</button>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const tooltipRef = ref()
const dynamicText = ref('初始文本')

const changeText = async () => {
  dynamicText.value = '这是一段更长的文本内容，用来测试动态更新的效果'
  await nextTick()
  // 文本改变后重新检查省略状态
  tooltipRef.value?.checkEllipsis()
}
</script>
```

### 不同行数对比

```vue
<template>
  <view class="demo-section">
    <view class="demo-item">
      <text class="label">1行省略：</text>
      <pro-tooltip :text="longText" :lines="1" />
    </view>
    
    <view class="demo-item">
      <text class="label">2行省略：</text>
      <pro-tooltip :text="longText" :lines="2" />
    </view>
    
    <view class="demo-item">
      <text class="label">3行省略：</text>
      <pro-tooltip :text="longText" :lines="3" />
    </view>
  </view>
</template>

<script setup>
const longText = '这是一段很长的文本内容，用来展示不同行数下的省略效果。文本足够长，可以清楚地看出1行、2行、3行省略的差异。点击被省略的文本可以查看完整内容。'
</script>
```

## 💡 高级用法

### 在列表中使用

```vue
<template>
  <view class="list">
    <view 
      v-for="item in list" 
      :key="item.id" 
      class="list-item"
    >
      <view class="title">{{ item.title }}</view>
      <pro-tooltip 
        :text="item.description"
        :lines="2"
        color="#666"
        fontSize="14px"
      />
    </view>
  </view>
</template>
```

### 结合其他组件

```vue
<template>
  <uv-list>
    <uv-list-item v-for="item in items" :key="item.id">
      <template #body>
        <view class="item-content">
          <pro-tooltip 
            :text="item.title"
            :lines="1"
            fontSize="16px"
            color="#333"
          />
          <pro-tooltip 
            :text="item.subtitle"
            :lines="2"
            fontSize="14px"
            color="#999"
          />
        </view>
      </template>
    </uv-list-item>
  </uv-list>
</template>
```

## ⚠️ 注意事项

### 智能检测机制
- 组件提供两种检测算法：
  - **高级检测** (默认): 区分中英文、数字、标点符号等不同字符类型，精确计算文本宽度
  - **简单检测**: 基于字符数量和字体大小的快速估算方法
- **短文本过滤**: 可配置最小检测长度(`minCheckLength`)，避免短文本误触发
- **自动响应**: 组件会自动监听文本和宽度变化，无需手动触发检测
- **精准判断**: 只有真正被省略的文本才会响应点击交互

### 性能优化建议
- 避免在大量列表项中同时使用，可考虑虚拟滚动
- 长文本建议设置合理的 `maxWidth` 避免影响布局
- 在文本频繁变化的场景中，可以使用防抖优化

### 平台兼容性
- **H5**: 完全支持，包括 hover 效果
- **小程序**: 支持基础功能，CSS 省略方式可能略有差异
- **APP**: 支持所有功能，使用 nvue 时省略样式会自动适配

### 样式注意事项
- 确保父容器有明确的宽度限制
- 避免在 `flex` 容器中使用时不设置 `flex-shrink`
- 文本内容包含特殊字符时可能影响省略效果

## 🎨 样式定制

### CSS 变量

```scss
.pro-tooltip-container {
  --tooltip-bg-color: rgba(0, 0, 0, 0.9);
  --tooltip-text-color: #fff;
  --tooltip-border-radius: 8px;
  --tooltip-padding: 12px 16px;
  --tooltip-font-size: 14px;
  --tooltip-line-height: 1.5;
  --tooltip-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --tooltip-z-index: 9999;
  --tooltip-arrow-size: 6px;
}
```

### 自定义主题

```vue
<template>
  <pro-tooltip 
    class="custom-tooltip"
    text="自定义主题的文本"
    :lines="2"
  />
</template>

<style lang="scss" scoped>
.custom-tooltip {
  :deep(.pro-tooltip-text) {
    font-family: 'Custom Font', sans-serif;
    letter-spacing: 0.5px;
  }
  
  :deep(.pro-tooltip-popup) {
    border-radius: 12px;
    backdrop-filter: blur(10px);
  }
}
</style>
```

## 🔧 开发指南

### 本地开发

```bash
# 在测试页面中查看效果
# 访问 /pages/tooltip-test/tooltip-test
```

### 调试技巧

```vue
<template>
  <pro-tooltip 
    ref="tooltipRef"
    :text="text"
    :lines="2"
    @click="debugClick"
  />
</template>

<script setup>
const tooltipRef = ref()

const debugClick = () => {
  console.log('是否省略:', tooltipRef.value?.isEllipsis())
}

// 手动触发检测
const manualCheck = () => {
  tooltipRef.value?.checkEllipsis()
}
</script>
```

## 📝 更新日志

### v1.2.0
- 🔧 **可配置检测**: 新增 `minCheckLength` 参数，可自定义最小检测长度
- 🚀 **双重算法**: 提供高级和简单两种检测算法，可通过 `useAdvancedDetection` 配置
- 🎯 **精确检测**: 高级算法区分中英文、数字、标点符号等字符类型
- ⚡ **性能平衡**: 可根据场景选择精度和性能的平衡点

### v1.1.0
- 🚀 **重大更新**: 点击切换显示/隐藏功能
- 🎯 **智能检测**: 短文本自动过滤，不响应点击
- ⚡ **性能优化**: 改进的省略检测算法
- 🔄 **自动监听**: 文本变化自动重新检测
- 🎨 **交互优化**: 完整的显示/隐藏切换体验

### v1.0.0
- 🎉 初始版本发布
- ✨ 支持单行和多行文本省略
- ✨ 智能检测文本省略状态
- ✨ 点击显示 tooltip 完整内容
- ✨ 支持丰富的样式自定义
- ✨ 多端兼容性优化

## 📄 许可证

MIT License

---

如果您在使用过程中遇到任何问题或有改进建议，欢迎提交 Issue 或 Pull Request！ 