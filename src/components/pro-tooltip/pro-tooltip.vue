<template>
  <view
    class="pro-tooltip-container"
    @click.stop="handleClick"
  >
    <text
      ref="textRef"
      class="pro-tooltip-text"
      :class="[`pro-tooltip-text--lines-${lines}`, { 'pro-tooltip-text--ellipsis': isEllipsis }]"
      :style="textStyle"
    >
      {{ text }}
    </text>

    <!-- Tooltip 弹层 -->
    <view
      v-if="showTooltip"
      class="pro-tooltip-popup"
      :style="tooltipStyle"
      @click.stop
    >
      <view class="pro-tooltip-content">
        <text class="pro-tooltip-full-text">{{ text }}</text>
      </view>
      <view
        class="pro-tooltip-arrow"
        :style="arrowStyle"
      ></view>
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="showTooltip"
      class="pro-tooltip-mask"
      @click.stop="hideTooltip"
    ></view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch, getCurrentInstance } from 'vue'

// 定义组件名称
defineOptions({
  name: 'ProTooltip'
})

// Props 定义
interface Props {
  text: string
  lines?: number
  disabled?: boolean
  maxWidth?: string | number
  fontSize?: string | number
  lineHeight?: string | number
  color?: string
  tooltipBgColor?: string
  tooltipTextColor?: string
  tooltipMaxWidth?: string | number
  clickable?: boolean
  minCheckLength?: number // 最小检测长度，短于此长度的文本不检测省略
  useAdvancedDetection?: boolean // 是否使用高级检测方法
}

const props = withDefaults(defineProps<Props>(), {
  lines: 1,
  disabled: false,
  maxWidth: '100%',
  fontSize: '14px',
  lineHeight: '1.4',
  color: '#333',
  tooltipBgColor: 'rgba(0, 0, 0, 0.8)',
  tooltipTextColor: '#fff',
  tooltipMaxWidth: '300px',
  clickable: true,
  minCheckLength: 10,
  useAdvancedDetection: true
})

// Emits 定义
const emit = defineEmits<{
  click: [text: string]
  'tooltip-show': [text: string]
  'tooltip-hide': [text: string]
}>()

// 响应式数据
const textRef = ref<any>(null)
const showTooltip = ref(false)
const isEllipsis = ref(false)
const tooltipPosition = ref({ x: 0, y: 0 })
const clickPosition = ref({ x: 0, y: 0 })
const isToggling = ref(false) // 防止重复触发

// 计算属性
const textStyle = computed(() => ({
  maxWidth: typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth,
  fontSize: typeof props.fontSize === 'number' ? `${props.fontSize}px` : props.fontSize,
  lineHeight: props.lineHeight,
  color: props.color
}))

const tooltipStyle = computed(() => ({
  left: `${tooltipPosition.value.x}px`,
  top: `${tooltipPosition.value.y}px`,
  backgroundColor: props.tooltipBgColor,
  color: props.tooltipTextColor,
  maxWidth: typeof props.tooltipMaxWidth === 'number' ? `${props.tooltipMaxWidth}px` : props.tooltipMaxWidth
}))

const arrowStyle = computed(() => ({
  borderTopColor: props.tooltipBgColor
}))

// 检查文本是否被省略
const checkEllipsis = async () => {
  if (!textRef.value || props.disabled) return

  await nextTick()

  try {
    // 使用更简单可靠的方法检测省略
    const query = uni.createSelectorQuery().in(getCurrentInstance())

    query
      .select('.pro-tooltip-text')
      .boundingClientRect((rect: any) => {
        if (rect) {
          if (props.lines === 1) {
            // 单行文本：通过文本长度和容器宽度估算
            checkSingleLineEllipsis(rect)
          } else {
            // 多行文本：比较实际高度和预期高度
            checkMultiLineEllipsis(rect)
          }
        }
      })
      .exec()
  } catch (error) {
    console.warn('Pro-tooltip: 检测文本省略状态失败', error)
  }
}

// 检查单行文本省略
const checkSingleLineEllipsis = (rect: any) => {
  const textLength = props.text.length

  // 如果文本很短，根据配置的最小检测长度判断
  if (textLength <= props.minCheckLength) {
    isEllipsis.value = false
    return
  }

  if (props.useAdvancedDetection) {
    // 使用高级检测方法：更精确的测量
    checkSingleLineEllipsisAdvanced(rect)
  } else {
    // 使用简单估算方法
    checkSingleLineEllipsisSimple(rect)
  }
}

// 高级检测方法：更精确的文本宽度计算
const checkSingleLineEllipsisAdvanced = (rect: any) => {
  const containerWidth = rect.width
  const fontSize = parseFloat(props.fontSize.toString().replace('px', ''))

  // 更精确的字符宽度计算 - 区分不同类型的字符
  const chineseChars = (props.text.match(/[\u4e00-\u9fa5]/g) || []).length
  const englishChars = (props.text.match(/[a-zA-Z]/g) || []).length
  const numberChars = (props.text.match(/[0-9]/g) || []).length
  const spaceChars = (props.text.match(/\s/g) || []).length
  const punctuationChars = (props.text.match(/[.,;:!?'"()[\]{}]/g) || []).length
  const otherChars = props.text.length - chineseChars - englishChars - numberChars - spaceChars - punctuationChars

  // 不同字符类型的宽度系数（相对于fontSize）
  // 这些系数基于常见字体的实际测量结果
  const textWidth =
    chineseChars * fontSize * 1.0 + // 中文字符 - 等宽
    englishChars * fontSize * 0.5 + // 英文字符 - 平均宽度
    numberChars * fontSize * 0.6 + // 数字字符 - 略宽于英文
    spaceChars * fontSize * 0.25 + // 空格字符 - 较窄
    punctuationChars * fontSize * 0.3 + // 标点符号 - 较窄
    otherChars * fontSize * 0.7 // 其他字符 - 中等宽度

  // 考虑可能的字符间距（letter-spacing）
  const estimatedLetterSpacing = 0 // 默认无额外间距
  const totalWidth = textWidth + (props.text.length - 1) * estimatedLetterSpacing

  // 判断是否超出容器宽度（预留15px容错空间，考虑padding和边距）
  isEllipsis.value = totalWidth > containerWidth - 15
}

// 简单估算方法：基于字符数量估算
const checkSingleLineEllipsisSimple = (rect: any) => {
  const fontSize = parseFloat(props.fontSize.toString().replace('px', ''))

  // 中文字符大约是fontSize宽度，英文字符大约是fontSize * 0.6宽度
  const chineseChars = (props.text.match(/[\u4e00-\u9fa5]/g) || []).length
  const otherChars = props.text.length - chineseChars
  const approximateWidth = chineseChars * fontSize + otherChars * fontSize * 0.6

  // 如果估算宽度大于容器宽度，则认为被省略了
  isEllipsis.value = approximateWidth > rect.width - 5 // 5px 容错
}

// 检查多行文本省略
const checkMultiLineEllipsis = (rect: any) => {
  // 计算预期高度
  const fontSize = parseFloat(props.fontSize.toString().replace('px', ''))
  const lineHeight = parseFloat(props.lineHeight.toString()) * fontSize
  const expectedHeight = lineHeight * props.lines

  // 如果实际高度大于预期高度，说明文本被省略了
  isEllipsis.value = rect.height > expectedHeight + 2 // 2px 容错
}

// 计算 tooltip 位置
const calculateTooltipPosition = (clickEvent: any) => {
  const { clientX, clientY } = clickEvent.touches?.[0] || clickEvent

  // 记录点击位置
  clickPosition.value = { x: clientX, y: clientY }

  // 获取页面尺寸
  const systemInfo = uni.getSystemInfoSync()
  const screenWidth = systemInfo.screenWidth
  const screenHeight = systemInfo.screenHeight

  // Tooltip 预估尺寸
  const tooltipWidth = 250 // 预估宽度
  const tooltipHeight = 100 // 预估高度
  const arrowHeight = 8

  let x = clientX - tooltipWidth / 2
  let y = clientY - tooltipHeight - arrowHeight - 10

  // 水平边界检查
  if (x < 10) {
    x = 10
  } else if (x + tooltipWidth > screenWidth - 10) {
    x = screenWidth - tooltipWidth - 10
  }

  // 垂直边界检查
  if (y < 10) {
    y = clientY + arrowHeight + 10 // 显示在点击位置下方
  }

  tooltipPosition.value = { x, y }
}

// 点击事件处理
const handleClick = (event: any) => {
  if (props.disabled || !props.clickable || isToggling.value) return

  // 防止重复触发
  isToggling.value = true

  emit('click', props.text)

  // 只有在文本被省略时才处理tooltip显示/隐藏
  if (isEllipsis.value) {
    if (showTooltip.value) {
      // 如果tooltip已经显示，则隐藏
      hideTooltip()
    } else {
      // 如果tooltip未显示，则显示
      calculateTooltipPosition(event)
      showTooltip.value = true
      emit('tooltip-show', props.text)
    }
  }

  // 延迟重置防抖标志
  setTimeout(() => {
    isToggling.value = false
  }, 300)
}

// 隐藏 tooltip
const hideTooltip = () => {
  if (!showTooltip.value) return // 如果已经隐藏，不重复执行

  showTooltip.value = false
  emit('tooltip-hide', props.text)
}

// 组件挂载后检查省略状态
onMounted(() => {
  checkEllipsis()
})

// 监听文本变化，重新检测省略状态
watch(
  () => props.text,
  () => {
    nextTick(() => {
      checkEllipsis()
    })
  }
)

// 监听maxWidth变化，重新检测省略状态
watch(
  () => props.maxWidth,
  () => {
    nextTick(() => {
      checkEllipsis()
    })
  }
)

// 暴露方法
defineExpose({
  checkEllipsis,
  hideTooltip,
  isEllipsis: () => isEllipsis.value
})
</script>

<style lang="scss" scoped>
.pro-tooltip-container {
  width: 100%;
  position: relative;
  display: inline-block;
  word-break: break-all;
}

.pro-tooltip-text {
  display: -webkit-box;
  word-break: break-all;
  overflow: hidden;

  &--ellipsis {
    cursor: pointer;
  }

  // 单行省略
  &--lines-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
  }

  // 多行省略
  &--lines-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &--lines-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  &--lines-4 {
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }

  &--lines-5 {
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
  }

  // 支持更多行数
  @for $i from 6 through 10 {
    &--lines-#{$i} {
      -webkit-line-clamp: #{$i};
      -webkit-box-orient: vertical;
    }
  }
}

.pro-tooltip-popup {
  position: fixed;
  z-index: 9999;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  word-break: break-all;
  animation: tooltipFadeIn 0.2s ease-out;
}

.pro-tooltip-content {
  position: relative;
}

.pro-tooltip-full-text {
  word-break: break-all;
  line-height: 1.5;
}

.pro-tooltip-arrow {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.8);
}

.pro-tooltip-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9998;
  background-color: transparent;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 不同平台的兼容性处理
/* #ifdef H5 */
.pro-tooltip-text {
  &:hover.pro-tooltip-text--ellipsis {
    opacity: 0.8;
  }
}
/* #endif */

/* #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ */
.pro-tooltip-text {
  &--lines-1 {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
/* #endif */

/* #ifdef APP-NVUE */
.pro-tooltip-text {
  &--lines-1 {
    lines: 1;
    text-overflow: ellipsis;
  }

  @for $i from 2 through 10 {
    &--lines-#{$i} {
      lines: #{$i};
      text-overflow: ellipsis;
    }
  }
}
/* #endif */
</style>
