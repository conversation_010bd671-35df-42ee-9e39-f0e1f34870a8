<template>
  <uv-list
    :border="border"
    :borderColor="borderColor"
    :customStyle="customStyle"
    class="pro-drag-list"
  >
    <view
      v-for="(item, index) in modelValue"
      :key="getItemKey(item, index)"
      class="pro-drag-list-item"
      :class="{
        'pro-drag-list-item--dragging': dragState.isDragging && dragState.dragIndex === index,
        'pro-drag-list-item--disabled': disabled,
        'pro-drag-list-item--recently-dragged': isRecentlyDragged(index)
      }"
      :style="getDragItemStyle(index)"
      @touchstart="handleTouchStart($event, index)"
      @touchmove="handleTouchMove($event, index)"
      @touchend="handleTouchEnd($event, index)"
      @touchcancel="handleTouchCancel($event, index)"
    >
      <uv-list-item
        :title="getItemProp(item, 'title')"
        :note="getItemProp(item, 'note')"
        :thumb="getItemProp(item, 'thumb')"
        :thumbSize="getItemProp(item, 'thumbSize', 'base')"
        :rightText="getItemProp(item, 'rightText')"
        :showArrow="showArrow"
        :showBadge="getItemProp(item, 'showBadge', false)"
        :badge="getItemProp(item, 'badge', {})"
        :showSwitch="getItemProp(item, 'showSwitch', false)"
        :switchChecked="getItemProp(item, 'switchChecked', false)"
        :showExtraIcon="getItemProp(item, 'showExtraIcon', false)"
        :extraIcon="getItemProp(item, 'extraIcon', {})"
        :disabled="disabled || getItemProp(item, 'disabled', false) || dragState.isDragging"
        :clickable="getItemClickable(index)"
        :border="itemBorder"
        :customStyle="getItemCustomStyle(item, index)"
        @click="handleItemClick(item, index)"
        @switchChange="handleSwitchChange(item, index, $event)"
      >
        <!-- 左侧内容插槽 -->
        <template #header>
          <view
            v-if="showDragHandle"
            class="pro-drag-handle"
          >
            <slot
              name="dragHandle"
              :item="item"
              :index="index"
            >
              <uv-icon
                name="menu"
                size="20"
                color="#999"
                class="pro-drag-handle-icon"
              />
            </slot>
          </view>
          <slot
            v-else
            name="left"
            :item="item"
            :index="index"
          ></slot>
        </template>

        <!-- 右侧内容插槽 -->
        <template #footer>
          <slot
            name="right"
            :item="item"
            :index="index"
          >
            <view
              v-if="getItemProp(item, 'rightText')"
              class="pro-drag-list-right-text"
            >
              {{ getItemProp(item, 'rightText') }}
            </view>
          </slot>
        </template>

        <!-- 自定义内容插槽 -->
        <template #body>
          <slot
            name="content"
            :item="item"
            :index="index"
          >
            <view class="pro-drag-list-content">
              <text
                v-if="getItemProp(item, 'title')"
                class="pro-drag-list-title"
                :class="[ellipsis && `uv-line-${ellipsis}`]"
              >
                {{ getItemProp(item, 'title') }}
              </text>
              <text
                v-if="getItemProp(item, 'note')"
                class="pro-drag-list-note"
              >
                {{ getItemProp(item, 'note') }}
              </text>
            </view>
          </slot>
        </template>
      </uv-list-item>
    </view>
  </uv-list>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

// 定义组件名称
defineOptions({
  name: 'ProDragList'
})

// 定义接口
interface DragState {
  isDragging: boolean
  dragIndex: number
  startY: number
  offsetY: number
  startTime: number
  startPageY: number
}

interface ListItem {
  [key: string]: any
}

// Props 定义
interface Props {
  modelValue: ListItem[]
  // 拖拽相关
  disabled?: boolean
  showDragHandle?: boolean
  dragTriggerDistance?: number
  dragTriggerTime?: number
  itemHeight?: number
  // uv-list 相关
  border?: boolean
  borderColor?: string
  customStyle?: Record<string, any>
  // uv-list-item 相关
  showArrow?: boolean
  clickable?: boolean
  itemBorder?: boolean
  ellipsis?: number | string
  // 数据相关
  keyField?: string
  titleField?: string
  noteField?: string
  thumbField?: string
  rightTextField?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showDragHandle: true,
  dragTriggerDistance: 10,
  dragTriggerTime: 150,
  itemHeight: 80,
  border: true,
  borderColor: '#dadbde',
  showArrow: false,
  clickable: false,
  itemBorder: true,
  ellipsis: 0,
  keyField: 'id',
  titleField: 'title',
  noteField: 'note',
  thumbField: 'thumb',
  rightTextField: 'rightText'
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: ListItem[]]
  'item-click': [item: ListItem, index: number]
  'switch-change': [item: ListItem, index: number, value: boolean]
  'drag-start': [item: ListItem, index: number]
  'drag-end': [item: ListItem, fromIndex: number, toIndex: number]
  'order-change': [list: ListItem[], fromIndex: number, toIndex: number]
}>()

// 拖拽状态
const dragState = reactive<DragState>({
  isDragging: false,
  dragIndex: -1,
  startY: 0,
  offsetY: 0,
  startTime: 0,
  startPageY: 0
})

// 获取项目键值
const getItemKey = (item: ListItem, index: number): string | number => {
  return item[props.keyField] || index
}

// 获取项目属性
const getItemProp = (item: ListItem, field: string, defaultValue: any = undefined): any => {
  // 如果是预定义的字段映射，使用字段映射
  const fieldMap: Record<string, string> = {
    title: props.titleField,
    note: props.noteField,
    thumb: props.thumbField,
    rightText: props.rightTextField
  }

  const actualField = fieldMap[field] || field
  return item[actualField] !== undefined ? item[actualField] : defaultValue
}

// 获取拖拽项目样式
const getDragItemStyle = (index: number) => {
  if (dragState.isDragging && dragState.dragIndex === index) {
    return {
      transform: `translateY(${dragState.offsetY}px)`,
      zIndex: 999,
      transition: 'none'
    }
  }
  return {
    transform: 'none',
    transition: 'transform 0.2s ease'
  }
}

// 获取项目自定义样式
const getItemCustomStyle = (item: ListItem, index: number) => {
  const baseStyle = {
    backgroundColor: '#fff'
  }

  if (dragState.isDragging && dragState.dragIndex === index) {
    return {
      ...baseStyle,
      opacity: 0.8,
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
      borderRadius: '8px'
    }
  }

  return baseStyle
}

// 获取项目是否可点击
const getItemClickable = (index: number) => {
  // 如果正在拖拽任何项目，所有项目都不可点击
  if (dragState.isDragging) return false
  // 如果刚刚完成拖拽，短暂禁用点击（防止意外触发hover）
  if (dragState.startTime > 0 && Date.now() - dragState.startTime < 200) return false
  return props.clickable
}

// 判断是否是刚完成拖拽的项目
const isRecentlyDragged = (index: number) => {
  return dragState.startTime > 0 && Date.now() - dragState.startTime < 200
}

// 触摸开始事件
const handleTouchStart = (event: TouchEvent, index: number) => {
  if (props.disabled) return

  const touch = event.touches[0]
  dragState.startY = touch.clientY
  dragState.startPageY = touch.pageY
  dragState.dragIndex = index
  dragState.startTime = Date.now()
  dragState.offsetY = 0
}

// 触摸移动事件
const handleTouchMove = (event: TouchEvent, index: number) => {
  if (props.disabled || dragState.dragIndex !== index) return

  const touch = event.touches[0]
  const deltaY = touch.clientY - dragState.startY
  const deltaTime = Date.now() - dragState.startTime

  // 检查是否应该开始拖拽
  if (!dragState.isDragging && Math.abs(deltaY) > props.dragTriggerDistance && deltaTime > props.dragTriggerTime) {
    dragState.isDragging = true
    emit('drag-start', props.modelValue[index], index)

    // 阻止页面滚动
    event.preventDefault()
  }

  if (dragState.isDragging) {
    dragState.offsetY = deltaY
    event.preventDefault()
  }
}

// 处理触摸取消事件 (当触摸被系统中断时)
const handleTouchCancel = (event: TouchEvent, index: number) => {
  if (dragState.dragIndex === index) {
    resetDragState()
  }
}

// 触摸结束事件
const handleTouchEnd = (event: TouchEvent, index: number) => {
  // 无论什么情况，如果是当前拖拽的项目，都要重置状态
  if (dragState.dragIndex === index) {
    if (props.disabled) {
      resetDragState()
      return
    }

    if (dragState.isDragging) {
      const moveDistance = Math.round(dragState.offsetY / props.itemHeight)

      if (moveDistance !== 0) {
        const newIndex = Math.max(0, Math.min(props.modelValue.length - 1, index + moveDistance))

        if (newIndex !== index) {
          // 执行排序
          const newList = [...props.modelValue]
          const [movedItem] = newList.splice(index, 1)
          newList.splice(newIndex, 0, movedItem)

          // 更新数据
          emit('update:modelValue', newList)
          emit('drag-end', movedItem, index, newIndex)
          emit('order-change', newList, index, newIndex)

          // 显示提示
          uni.showToast({
            title: `已移动到第${newIndex + 1}位`,
            icon: 'none',
            duration: 1500
          })
        }
      }
    }

    // 重置拖拽状态
    resetDragState()
  }
}

// 重置拖拽状态
const resetDragState = () => {
  const wasDragging = dragState.isDragging
  const dragIndex = dragState.dragIndex

  dragState.isDragging = false
  dragState.dragIndex = -1
  dragState.startY = 0
  dragState.offsetY = 0
  dragState.startTime = Date.now() // 记录重置时间，用于短暂禁用点击
  dragState.startPageY = 0

  // 如果刚完成拖拽，延迟一小段时间确保 hover 状态完全重置
  if (wasDragging && dragIndex !== -1) {
    setTimeout(() => {
      // 强制重置可能残留的状态
      dragState.startTime = 0
    }, 200)
  }
}

// 项目点击事件
const handleItemClick = (item: ListItem, index: number) => {
  if (dragState.isDragging || props.disabled) return
  emit('item-click', item, index)
}

// 开关变化事件
const handleSwitchChange = (item: ListItem, index: number, event: any) => {
  if (props.disabled) return
  emit('switch-change', item, index, event.value)
}

// 暴露方法
defineExpose({
  resetDragState,
  getDragState: () => ({ ...dragState })
})
</script>

<style lang="scss" scoped>
.pro-drag-list {
  position: relative;
}

.pro-drag-list-item {
  position: relative;
  transition: transform 0.2s ease;

  &--dragging {
    z-index: 999;
    opacity: 0.8;

    :deep(.uv-list-item) {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      border-radius: 8px;
    }
  }

  &--disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  // 重置 hover 状态，确保拖拽后背景恢复正常
  :deep(.uv-list-item) {
    background-color: #fff;
    transition: background-color 0.2s ease;

    &.uv-list-item--hover {
      background-color: #f1f1f1;
    }
  }

  // 拖拽完成后的临时状态，禁用hover效果
  &.pro-drag-list-item--recently-dragged {
    :deep(.uv-list-item) {
      &.uv-list-item--hover {
        background-color: #fff !important;
      }
    }
  }
}

.pro-drag-handle {
  display: flex;
  align-items: center;
  padding: 0 8px;
  margin-right: 8px;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }

  .pro-drag-handle-icon {
    color: #999;
  }
}

.pro-drag-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pro-drag-list-title {
  font-size: 14px;
  color: #3b4144;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pro-drag-list-note {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pro-drag-list-right-text {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

/* 行数限制样式 */
.uv-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uv-line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.uv-line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
