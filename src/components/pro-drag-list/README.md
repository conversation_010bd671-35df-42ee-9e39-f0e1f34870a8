# ProDragList 可拖拽列表组件

一个基于 `uv-list` 和 `uv-list-item` 的高阶可拖拽列表组件，支持长按拖拽排序、丰富的配置选项和完整的 TypeScript 类型支持。

## ✨ 特性

- 🎯 **基于 uv-list**: 完全兼容原有的 uv-list 和 uv-list-item 组件
- 🔄 **拖拽排序**: 支持长按拖拽重新排序列表项
- 🎨 **视觉反馈**: 拖拽时提供丰富的视觉反馈效果
- 🏷️ **数据映射**: 灵活的数据字段映射，适配不同数据结构
- 📱 **移动端优化**: 专为移动端触摸操作优化
- 🔧 **高度可配置**: 丰富的配置选项和插槽支持
- 📝 **TypeScript**: 完整的 TypeScript 类型定义
- 🎪 **事件丰富**: 提供完整的拖拽生命周期事件

## 📦 安装使用

```vue
<template>
  <pro-drag-list
    v-model="list"
    :showDragHandle="true"
    :clickable="true"
    @item-click="handleItemClick"
    @order-change="handleOrderChange"
  />
</template>

<script setup>
import ProDragList from '@/components/pro-drag-list/pro-drag-list.vue'

const list = ref([
  { id: 1, title: '项目1', note: '描述1' },
  { id: 2, title: '项目2', note: '描述2' }
])

const handleItemClick = (item, index) => {
  console.log('点击项目', item, index)
}

const handleOrderChange = (newList, fromIndex, toIndex) => {
  console.log('排序变化', newList, fromIndex, toIndex)
}
</script>
```

## 📚 API 文档

### Props 属性

#### 基础属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `Array` | `[]` | 列表数据，支持 v-model |
| `disabled` | `Boolean` | `false` | 是否禁用拖拽功能 |

#### 拖拽配置
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showDragHandle` | `Boolean` | `true` | 是否显示拖拽手柄 |
| `dragTriggerDistance` | `Number` | `10` | 触发拖拽的最小距离(px) |
| `dragTriggerTime` | `Number` | `150` | 触发拖拽的最小时间(ms) |
| `itemHeight` | `Number` | `80` | 列表项高度(px)，用于计算拖拽距离 |

#### uv-list 相关属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `border` | `Boolean` | `true` | 是否显示列表边框 |
| `borderColor` | `String` | `#dadbde` | 边框颜色 |
| `customStyle` | `Object` | `{}` | 自定义样式 |

#### uv-list-item 相关属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showArrow` | `Boolean` | `false` | 是否显示右侧箭头 |
| `clickable` | `Boolean` | `false` | 是否可点击 |
| `itemBorder` | `Boolean` | `true` | 是否显示项目边框 |
| `ellipsis` | `Number\|String` | `0` | 文字省略行数 |

#### 数据映射
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `keyField` | `String` | `id` | 键值字段名 |
| `titleField` | `String` | `title` | 标题字段名 |
| `noteField` | `String` | `note` | 描述字段名 |
| `thumbField` | `String` | `thumb` | 头像字段名 |
| `rightTextField` | `String` | `rightText` | 右侧文字字段名 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: Array)` | 数据更新事件，支持 v-model |
| `item-click` | `(item: Object, index: Number)` | 列表项点击事件 |
| `switch-change` | `(item: Object, index: Number, value: Boolean)` | 开关变化事件 |
| `drag-start` | `(item: Object, index: Number)` | 拖拽开始事件 |
| `drag-end` | `(item: Object, fromIndex: Number, toIndex: Number)` | 拖拽结束事件 |
| `order-change` | `(list: Array, fromIndex: Number, toIndex: Number)` | 排序变化事件 |

### Slots 插槽

| 插槽名 | 作用域参数 | 说明 |
|--------|------------|------|
| `dragHandle` | `{ item, index }` | 自定义拖拽手柄 |
| `left` | `{ item, index }` | 左侧内容插槽 |
| `right` | `{ item, index }` | 右侧内容插槽 |
| `content` | `{ item, index }` | 自定义内容插槽 |

### Methods 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `resetDragState` | - | - | 重置拖拽状态 |
| `getDragState` | - | `Object` | 获取当前拖拽状态 |

## 🎯 使用示例

### 基础使用

```vue
<template>
  <pro-drag-list
    v-model="basicList"
    :showDragHandle="true"
    @order-change="onOrderChange"
  />
</template>

<script setup>
const basicList = ref([
  { id: 1, title: '第一项', note: '这是第一项的描述' },
  { id: 2, title: '第二项', note: '这是第二项的描述' },
  { id: 3, title: '第三项', note: '这是第三项的描述' }
])

const onOrderChange = (newList, fromIndex, toIndex) => {
  console.log(`从位置 ${fromIndex} 移动到位置 ${toIndex}`)
}
</script>
```

### 自定义字段映射

```vue
<template>
  <pro-drag-list
    v-model="customList"
    keyField="userId"
    titleField="userName"
    noteField="userRole"
    thumbField="userAvatar"
    rightTextField="userTime"
  />
</template>

<script setup>
const customList = ref([
  {
    userId: 1,
    userName: '张三',
    userRole: '前端开发',
    userAvatar: 'avatar1.jpg',
    userTime: '09:30'
  }
])
</script>
```

### 自定义拖拽手柄

```vue
<template>
  <pro-drag-list v-model="list">
    <template #dragHandle="{ item, index }">
      <view class="custom-handle">
        <uv-icon name="drag" size="24" color="#666" />
      </view>
    </template>
  </pro-drag-list>
</template>
```

### 自定义内容

```vue
<template>
  <pro-drag-list v-model="list">
    <template #content="{ item, index }">
      <view class="custom-content">
        <text class="title">{{ item.title }}</text>
        <text class="subtitle">{{ item.subtitle }}</text>
        <view class="tags">
          <text v-for="tag in item.tags" :key="tag" class="tag">
            {{ tag }}
          </text>
        </view>
      </view>
    </template>
  </pro-drag-list>
</template>
```

### 右侧自定义内容

```vue
<template>
  <pro-drag-list v-model="list">
    <template #right="{ item, index }">
      <view class="custom-right">
        <uv-button size="mini" @click="editItem(item)">编辑</uv-button>
        <uv-button size="mini" type="error" @click="deleteItem(index)">删除</uv-button>
      </view>
    </template>
  </pro-drag-list>
</template>
```

### 完整配置示例

```vue
<template>
  <pro-drag-list
    v-model="fullList"
    :border="true"
    :showDragHandle="true"
    :clickable="true"
    :showArrow="true"
    :itemHeight="100"
    :dragTriggerDistance="15"
    :dragTriggerTime="200"
    keyField="id"
    titleField="name"
    noteField="description"
    thumbField="avatar"
    rightTextField="status"
    @item-click="handleClick"
    @drag-start="handleDragStart"
    @drag-end="handleDragEnd"
    @order-change="handleOrderChange"
  />
</template>

<script setup>
const fullList = ref([
  {
    id: 1,
    name: '项目名称',
    description: '项目描述信息',
    avatar: 'https://example.com/avatar.jpg',
    status: '进行中'
  }
])

const handleClick = (item, index) => {
  console.log('点击项目', item, index)
}

const handleDragStart = (item, index) => {
  console.log('开始拖拽', item.name)
}

const handleDragEnd = (item, fromIndex, toIndex) => {
  console.log('拖拽结束', item.name, fromIndex, toIndex)
}

const handleOrderChange = (newList, fromIndex, toIndex) => {
  console.log('排序变更', newList)
  // 这里可以调用 API 保存新的排序
  saveOrder(newList)
}
</script>
```

## ⚠️ 注意事项

### 数据结构要求
- 列表数据必须是数组格式
- 每个列表项必须有唯一的键值字段（默认为 `id`）
- 建议使用 `v-model` 绑定数据以确保响应式更新

### 拖拽行为
- 长按 150ms 且移动距离超过 10px 才会触发拖拽
- 拖拽时会阻止页面滚动
- 拖拽完成后会有 200ms 的缓冲期防止意外点击

### 性能优化
- 组件内部使用了 `reactive` 和适当的计算属性优化性能
- 大数据量时建议设置合适的 `itemHeight` 以提高拖拽计算精度
- 避免在拖拽过程中进行复杂的数据操作

### 兼容性
- 支持 Vue 3 + Composition API
- 兼容 UniApp 各端
- 需要 uv-ui 组件库支持

## 🔧 开发指南

### 本地开发
```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 贡献代码
1. Fork 项目
2. 创建特性分支
3. 提交代码
4. 发起 Pull Request

## 📝 更新日志

### v1.0.0
- 🎉 初始版本发布
- ✨ 支持基础拖拽排序功能
- ✨ 支持自定义字段映射
- ✨ 支持丰富的插槽配置
- ✨ 完整的 TypeScript 类型支持

## 📄 许可证

MIT License

## 🤝 贡献者

感谢所有为这个项目做出贡献的开发者们！

---

如果您在使用过程中遇到任何问题或有改进建议，欢迎提交 Issue 或 Pull Request！ 