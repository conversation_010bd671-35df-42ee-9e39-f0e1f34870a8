<template>
  <view class="bottom-btns" :class="{isTabbarPage: isTabbarPage}">
    <slot></slot>
  </view>
</template>
<script setup>
import { ref, onMounted } from 'vue'
const TABBAR_URLS = ['pages/home/<USER>','pages/activity/index','pages/userCenter/index']

const isTabbarPage = ref(false)

onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const pageUrl = currentPage.route;
  if (TABBAR_URLS.includes(pageUrl)) {
    isTabbarPage.value = true
  }else{
    isTabbarPage.value = false
  }
})
</script>
<style scoped lang="scss">
.bottom-btns {
  z-index: 10;
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx 32rpx 16rpx 32rpx;
  padding-bottom: calc(8rpx + env(safe-area-inset-bottom));
  background-color: #FFFFFF;
  box-sizing: border-box;
  border-radius: 32rpx 32rpx 0 0;
  &.isTabbarPage {
    padding-bottom: 16rpx!important;
  }
}
</style>
