# ProEmpty 空状态组件

基于 uv-empty 的增强版空状态组件，提供更丰富的文案配置和交互功能。

## ✨ 特性

- 🎯 **基于uv-empty**: 继承所有uv-empty的功能和特性
- 📝 **增强文案配置**: 支持标题、描述文本的独立配置和样式设置
- 🎛️ **预设场景**: 内置多种常用场景配置，开箱即用
- 🖼️ **精美图标**: 部分场景配置了专门设计的自定义图标
- 🎨 **按钮集成**: 内置按钮支持，可配置各种按钮样式和状态
- 🔧 **插槽扩展**: 提供多个插槽支持复杂的自定义操作区域
- 🎪 **智能配置**: 场景化配置自动匹配图标、文案和按钮文字
- 📱 **移动端优化**: 专为移动端设计的交互体验
- 🚀 **TypeScript**: 完整的类型定义支持

## 📦 基础用法

```vue
<template>
  <!-- 默认空状态 -->
  <pro-empty />
  
  <!-- 自定义文案 -->
  <pro-empty
    title="没有找到数据"
    description="请尝试刷新页面或检查网络连接"
  />
  
  <!-- 带按钮的空状态 -->
  <pro-empty
    scene="network"
    :showButton="true"
    @button-click="handleRetry"
  />
</template>

<script setup>
import ProEmpty from '@/components/pro-empty/pro-empty.vue'

const handleRetry = () => {
  console.log('重试操作')
}
</script>
```

## 📚 API 文档

### Props 属性

#### 基础属性 (继承自 uv-empty)
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `icon` | `String` | `''` | 自定义图标路径 |
| `text` | `String` | `''` | 主要文本内容 |
| `textColor` | `String` | `'#909399'` | 文字颜色 |
| `textSize` | `String\|Number` | `14` | 文字大小 |
| `iconColor` | `String` | `'#c0c4cc'` | 图标颜色 |
| `iconSize` | `String\|Number` | `90` | 图标大小 |
| `mode` | `String` | `'data'` | 预置图标类型 |
| `width` | `String\|Number` | `160` | 图标宽度 |
| `height` | `String\|Number` | `160` | 图标高度 |
| `show` | `Boolean` | `true` | 是否显示组件 |
| `marginTop` | `String\|Number` | `0` | 上边距 |
| `customStyle` | `Object` | `{}` | 自定义样式 |

#### 扩展属性
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | `String` | `''` | 主标题（优先级高于text） |
| `description` | `String` | `''` | 描述文本 |
| `descriptionColor` | `String` | `'#c0c4cc'` | 描述文本颜色 |
| `descriptionSize` | `String\|Number` | `12` | 描述文本大小 |
| `scene` | `String` | `'data'` | 预设场景类型 |

#### 按钮配置
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showButton` | `Boolean` | `false` | 是否显示按钮 |
| `buttonText` | `String` | `'重新加载'` | 按钮文字 |
| `buttonType` | `String` | `'primary'` | 按钮类型 |
| `buttonSize` | `String` | `'small'` | 按钮大小 |
| `buttonPlain` | `Boolean` | `false` | 是否为朴素按钮 |
| `buttonLoading` | `Boolean` | `false` | 按钮加载状态 |
| `buttonDisabled` | `Boolean` | `false` | 按钮禁用状态 |
| `buttonCustomStyle` | `Object` | `{}` | 按钮自定义样式 |

### 预设场景类型

ProEmpty 组件内置了多种常用场景，每个场景都预配置了合适的图标、文案和按钮文字：

| 场景值 | 说明 | 图标类型 | 默认文案 | 默认按钮文字 |
|--------|------|----------|----------|-------------|
| `data` | 数据为空 | 自定义图标 ⭐ | 暂无数据 | 刷新数据 |
| `search` | 搜索无结果 | 自定义图标 ⭐ | 无搜索结果 | 重新搜索 |
| `network` | 网络异常 | `wifi-off` | 网络异常 | 重新连接 |
| `error` | 页面错误 | 自定义图标 ⭐ | 页面加载失败 | 重新加载 |
| `permission` | 无权限 | `permission` | 暂无权限 | 申请权限 |
| `cart` | 购物车空 | `car` | 购物车为空 | 去购物 |
| `order` | 订单为空 | `order` | 暂无订单 | 去下单 |
| `custom` | 自定义 | `data` | - | 确定 |

> ⭐ **自定义图标说明**：
> - `data` 和 `search` 场景使用：`empty_data.svg`
> - `error` 场景使用：`empty_error.png`
> - 这些图标经过精心设计，提供更好的视觉体验

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | - | 点击空状态区域时触发 |
| `button-click` | - | 点击按钮时触发 |

### Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| `default` | 默认插槽，添加自定义内容 |
| `button` | 自定义按钮区域 |
| `action` | 自定义操作区域 |

## 🎯 使用示例

### 预设场景快速使用

```vue
<template>
  <!-- 数据为空场景 -->
  <pro-empty scene="data" />
  
  <!-- 搜索无结果场景 -->
  <pro-empty scene="search" />
  
  <!-- 网络异常场景，带重试按钮 -->
  <pro-empty 
    scene="network" 
    :showButton="true"
    @button-click="handleRetry"
  />
  
  <!-- 权限不足场景 -->
  <pro-empty scene="permission" />
</template>
```

### 自定义文案和样式

```vue
<template>
  <pro-empty
    title="暂无相关数据"
    description="当前筛选条件下没有找到匹配的内容，请尝试调整筛选条件"
    textColor="#1890ff"
    :textSize="16"
    descriptionColor="#999"
    :descriptionSize="13"
    iconColor="#52c41a"
    :iconSize="100"
  />
</template>
```

### 带按钮交互

```vue
<template>
  <pro-empty
    scene="error"
    :showButton="true"
    buttonText="重新加载"
    buttonType="primary"
    :buttonLoading="loading"
    @button-click="handleReload"
  />
</template>

<script setup>
import { ref } from 'vue'

const loading = ref(false)

const handleReload = async () => {
  loading.value = true
  try {
    // 执行重新加载逻辑
    await reloadData()
  } finally {
    loading.value = false
  }
}
</script>
```

### 自定义按钮区域

```vue
<template>
  <pro-empty
    scene="error"
    title="网络连接失败"
    description="请检查网络设置后重试"
  >
    <template #button>
      <view class="custom-buttons">
        <uv-button 
          text="重新加载" 
          type="primary"
          @click="handleReload"
        />
        <uv-button 
          text="返回首页" 
          type="default" 
          plain
          @click="handleGoHome"
        />
      </view>
    </template>
  </pro-empty>
</template>

<style>
.custom-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
```

### 自定义操作区域

```vue
<template>
  <pro-empty
    scene="permission"
    title="访问受限"
    description="您当前没有权限访问此功能"
  >
    <template #action>
      <view class="action-area">
        <uv-button text="申请权限" type="primary" size="small" />
        <uv-button text="联系管理员" type="default" size="small" plain />
        <uv-button text="查看帮助" type="info" size="small" plain />
      </view>
    </template>
  </pro-empty>
</template>

<style>
.action-area {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}
</style>
```

### 列表页面集成

```vue
<template>
  <view class="page">
    <!-- 数据列表 -->
    <view v-if="list.length > 0" class="list">
      <view v-for="item in list" :key="item.id" class="list-item">
        {{ item.name }}
      </view>
    </view>
    
    <!-- 空状态 -->
    <pro-empty
      v-else
      :scene="getEmptyScene()"
      :showButton="needRetryButton"
      @button-click="handleEmptyAction"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

const list = ref([])
const loading = ref(false)
const error = ref(false)

// 根据状态确定空状态场景
const getEmptyScene = () => {
  if (error.value) return 'network'
  if (list.value.length === 0) return 'data'
  return 'data'
}

// 是否需要重试按钮
const needRetryButton = computed(() => {
  return error.value || list.value.length === 0
})

// 处理空状态操作
const handleEmptyAction = () => {
  if (error.value) {
    // 网络错误时重试
    loadData()
  } else {
    // 数据为空时刷新
    loadData()
  }
}
</script>
```

## 💡 高级用法

### 动态场景切换

```vue
<template>
  <pro-empty
    :scene="currentScene"
    :title="customTitle"
    :description="customDescription"
    :showButton="showActionButton"
    :buttonLoading="actionLoading"
    @button-click="handleAction"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const status = ref('loading') // loading, empty, error, permission

const currentScene = computed(() => {
  switch (status.value) {
    case 'empty': return 'data'
    case 'error': return 'network'
    case 'permission': return 'permission'
    default: return 'data'
  }
})

const customTitle = computed(() => {
  switch (status.value) {
    case 'empty': return '暂无数据'
    case 'error': return '加载失败'
    case 'permission': return '访问受限'
    default: return ''
  }
})

// ... 其他逻辑
</script>
```

### 响应式适配

```vue
<template>
  <pro-empty
    scene="data"
    :iconSize="responsiveIconSize"
    :textSize="responsiveTextSize"
    :customStyle="responsiveStyle"
  />
</template>

<script setup>
import { computed } from 'vue'

// 响应式图标大小
const responsiveIconSize = computed(() => {
  const width = uni.getSystemInfoSync().windowWidth
  return width < 375 ? 80 : 100
})

// 响应式文字大小
const responsiveTextSize = computed(() => {
  const width = uni.getSystemInfoSync().windowWidth
  return width < 375 ? 13 : 15
})

// 响应式样式
const responsiveStyle = computed(() => ({
  padding: '40px 20px'
}))
</script>
```

## ⚠️ 注意事项

### 使用建议
- 优先使用预设场景，可以保证设计一致性
- 自定义文案时注意文字长度，避免在小屏设备上显示异常
- 按钮文字建议控制在6个字符以内
- 在列表页面中使用时，建议根据实际的错误状态选择合适的场景

### 性能优化
- 空状态组件通常在数据加载完成后显示，避免频繁切换
- 大量使用时可以考虑按需引入特定场景的配置
- 自定义插槽内容过多时注意性能影响

### 平台兼容性
- 基于 uv-empty 构建，继承其平台兼容性
- 在不同平台上按钮样式可能略有差异
- 小程序平台建议测试自定义插槽的兼容性

## 📝 更新日志

### v1.1.0
- 🖼️ 为 `data`、`search`、`error` 场景添加了自定义图标
- 🎨 优化按钮默认大小为 `small`，适配移动端体验
- 📝 完善文档说明，添加自定义图标使用指南

### v1.0.0
- 🎉 基于 uv-empty 的首个版本
- ✨ 支持丰富的文案配置功能
- ✨ 内置多种预设场景
- ✨ 完整的按钮集成支持
- ✨ 灵活的插槽扩展机制
- ✨ TypeScript 类型定义支持

## 📄 许可证

MIT License

---

如果您在使用过程中遇到任何问题或有改进建议，欢迎提交 Issue 或 Pull Request！ 