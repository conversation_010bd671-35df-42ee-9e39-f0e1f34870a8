<template>
  <uv-empty
    :icon="currentIcon"
    :text="currentText"
    :textColor="textColor"
    :textSize="textSize"
    :iconColor="iconColor"
    :iconSize="iconSize"
    :mode="currentMode"
    :width="width"
    :height="height"
    :show="show"
    :marginTop="marginTop"
    :customStyle="customStyle"
    @click="handleEmptyClick"
  >
    <view class="pro-empty__content">
      <!-- 自定义描述文本 -->
      <view
        v-if="currentDescription"
        class="pro-empty__description"
      >
        <text :style="descriptionStyle">{{ currentDescription }}</text>
      </view>

      <!-- 按钮区域 -->
      <view
        v-if="showButton || $slots.button"
        class="pro-empty__button-area"
      >
        <slot name="button">
          <uv-button
            v-if="showButton"
            :text="currentButtonText"
            :type="buttonType"
            :size="buttonSize"
            :plain="buttonPlain"
            :loading="buttonLoading"
            :disabled="buttonDisabled"
            :customStyle="buttonCustomStyle"
            @click="handleButtonClick"
          />
        </slot>
      </view>

      <!-- 自定义操作区域 -->
      <view
        v-if="$slots.action"
        class="pro-empty__action-area"
      >
        <slot name="action"></slot>
      </view>

      <!-- 默认插槽 -->
      <slot></slot>
    </view>
  </uv-empty>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义组件名称
defineOptions({
  name: 'ProEmpty'
})

// Props 定义
interface Props {
  // 基础uv-empty属性
  icon?: string
  text?: string
  textColor?: string
  textSize?: string | number
  iconColor?: string
  iconSize?: string | number
  mode?: string
  width?: string | number
  height?: string | number
  show?: boolean
  marginTop?: string | number
  customStyle?: Record<string, any>

  // 扩展属性
  title?: string // 主标题
  description?: string // 描述文本
  descriptionColor?: string // 描述文本颜色
  descriptionSize?: string | number // 描述文本大小

  // 按钮配置
  showButton?: boolean // 是否显示按钮
  buttonText?: string // 按钮文字
  buttonType?: string // 按钮类型
  buttonSize?: string // 按钮大小
  buttonPlain?: boolean // 是否为朴素按钮
  buttonLoading?: boolean // 按钮加载状态
  buttonDisabled?: boolean // 按钮禁用状态
  buttonCustomStyle?: Record<string, any> // 按钮自定义样式

  // 预设场景配置
  scene?: 'data' | 'search' | 'network' | 'error' | 'permission' | 'cart' | 'order' | 'custom'
}

const props = withDefaults(defineProps<Props>(), {
  // uv-empty 默认值
  icon: '',
  text: '',
  textColor: '#909399',
  textSize: 14,
  iconColor: '#c0c4cc',
  iconSize: 90,
  mode: 'data',
  width: 160,
  height: 160,
  show: true,
  marginTop: 0,

  // 扩展属性默认值
  title: '',
  description: '',
  descriptionColor: '#c0c4cc',
  descriptionSize: 12,

  // 按钮默认值
  showButton: false,
  buttonText: '重新加载',
  buttonType: 'primary',
  buttonSize: 'small',
  buttonPlain: false,
  buttonLoading: false,
  buttonDisabled: false,

  // 场景默认值
  scene: 'data'
})

// Emits 定义
const emit = defineEmits<{
  click: []
  'button-click': []
}>()

// 预设场景配置
const sceneConfigs: Record<
  string,
  {
    mode: string
    icon?: string
    text: string
    description: string
    buttonText: string
  }
> = {
  data: {
    mode: 'data',
    icon: 'https://oss-public.yunlizhi.cn/frontend/delivery-center/uni-app/IOT/empty_data.svg',
    text: '暂无数据',
    description: '当前没有可显示的内容',
    buttonText: '刷新数据'
  },
  search: {
    mode: 'search',
    icon: 'https://oss-public.yunlizhi.cn/frontend/delivery-center/uni-app/IOT/empty_data.svg',
    text: '无搜索结果',
    description: '换个关键词试试吧',
    buttonText: '重新搜索'
  },
  network: {
    mode: 'wifi-off',
    text: '网络异常',
    description: '请检查网络连接后重试',
    buttonText: '重新连接'
  },
  error: {
    mode: 'page',
    icon: 'https://oss-public.yunlizhi.cn/frontend/delivery-center/uni-app/IOT/empty_error.png',
    text: '页面加载失败',
    description: '页面出现了一些问题',
    buttonText: '重新加载'
  },
  permission: {
    mode: 'permission',
    text: '暂无权限',
    description: '您当前没有访问权限',
    buttonText: '申请权限'
  },
  cart: {
    mode: 'car',
    text: '购物车为空',
    description: '快去挑选心仪的商品吧',
    buttonText: '去购物'
  },
  order: {
    mode: 'order',
    text: '暂无订单',
    description: '您还没有相关的订单记录',
    buttonText: '去下单'
  },
  custom: {
    mode: 'data',
    text: '',
    description: '',
    buttonText: '确定'
  }
}

// 计算当前配置
const currentConfig = computed(() => {
  return sceneConfigs[props.scene] || sceneConfigs.data
})

// 计算当前模式
const currentMode = computed(() => {
  return props.mode || currentConfig.value.mode
})

// 计算当前图标
const currentIcon = computed(() => {
  return props.icon || currentConfig.value.icon || ''
})

// 计算当前文本
const currentText = computed(() => {
  return props.title || props.text || currentConfig.value.text
})

// 计算描述样式
const descriptionStyle = computed(() => ({
  color: props.descriptionColor,
  fontSize: typeof props.descriptionSize === 'number' ? `${props.descriptionSize}px` : props.descriptionSize
}))

// 计算按钮自定义样式
const buttonCustomStyle = computed(() => ({
  marginTop: '20px',
  ...props.buttonCustomStyle
}))

// 处理空状态点击
const handleEmptyClick = () => {
  emit('click')
}

// 处理按钮点击
const handleButtonClick = () => {
  emit('button-click')
}

// 计算当前描述文本
const currentDescription = computed(() => {
  return props.description || currentConfig.value.description
})

// 计算当前按钮文字
const currentButtonText = computed(() => {
  return props.buttonText || currentConfig.value.buttonText
})
</script>

<style lang="scss" scoped>
.pro-empty__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.pro-empty__description {
  margin-top: 12px;
  text-align: center;
  line-height: 1.4;
}

.pro-empty__button-area {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pro-empty__action-area {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}
</style>
