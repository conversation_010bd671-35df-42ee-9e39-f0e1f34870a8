<template>
  <uv-modal ref="modalRef" width="622rpx" :closeOnClickOverlay="false" :showConfirmButton="false" :showCancelButton="false">
    <view class="modal-wrapper">
      <view class="modal-header">
        <view class="header-title">
          {{ configRef.title }}
        </view>
      </view>
      <view class="modal-content">
        <rich-text class="content-style" v-if="configRef.content && isHtml(configRef.content)" nodes="{{configRef.content}}"></rich-text>
        <view class="content-style" v-else-if="configRef.content && !isHtml(configRef.content)">{{configRef.content}}</view>
        <slot v-else></slot>
      </view>
      <view class="modal-footer">
        <pro-button v-if="configRef.showCancelBtn" @tap="handleCancel" :plain="true" :hairline="true" :text="configRef.cancelBtnText"></pro-button>
        <pro-button v-if="configRef.showConfirmBtn" @tap="handleConfirm" type="primary" :text="configRef.confirmBtnText"></pro-button>
      </view>
    </view>
  </uv-modal>
</template>
<script setup>
import { ref, toRefs, computed } from 'vue';
const props = defineProps({
  config: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const { config } = toRefs(props)

const configRef = computed(()=>{
  return {
    cancelBtnText:'取消',
    confirmBtnText:'确定',
    showCancelBtn:true,
    showConfirmBtn:true,
    confirmWithClose: true,
    title:'标题',
    ...config.value
  }
})

const modalRef = ref(null)

const emits = defineEmits(['onCancel', 'onConfirm'])

function show(){
  modalRef.value.open()
}

function close(){
  modalRef.value.close()
}

function isHtml(str) {
  return str.includes('<div') || str.includes('<p')
}

function handleCancel(){
  emits('onCancel')
  close()
}

function handleConfirm(){
  if(configRef.value.confirmWithClose){
    close()
  }
  emits('onConfirm')
}

defineExpose({
  show,
  close
})
</script>
<style lang="scss" scoped>
.modal-wrapper {
  width: 100%;
  box-sizing: border-box;
  .modal-header {
    text-align: center;
    .header-title {
      font-size: 36rpx;
      color: #1C2026;
      font-weight: 600;
      line-height: 52rpx;
    }
  }

  .modal-content {
    margin: 16rpx 0 24rpx 0;
    .content-style {
      font-size: 32rpx;
      font-weight: 400;
      color: #1C2026;
      line-height: 48rpx;
    }
  }

  .modal-footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24rpx;
  }
}



</style>
