<template>
  <view>
    <uv-popup
      v-model="showPopup"
      mode="bottom"
      border-radius="14"
      :mask-close-able="false"
    >
      <view class="h-[1000rpx] p-[40rpx]">
        <view class="flex items-center">
          <image
            class="w-[100rpx] h-[100rpx] rounded"
            mode="heightFix"
            :src="logo"
          ></image>
          <text class="ml-5 text-3xl font-bold">{{ title }}</text>
        </view>
        <view class="mt-5 text-muted">建议使用您的微信头像和昵称，以便获得更好的体验</view>
        <view class="mt-[30rpx]">
          <uv-form
            ref="formRef"
            :model="formData"
            :rules="rules"
          >
            <uv-form-item
              prop="avatar"
              label="头像"
              :labelWidth="120"
            >
              <view class="flex-1">
                <avatar-upload v-model="formData.avatar"></avatar-upload>
              </view>
            </uv-form-item>
            <uv-form-item
              label="昵称"
              :labelWidth="120"
            >
              <uv-input
                class="flex-1 h-[60rpx]"
                v-model="formData.nickname"
                placeholder="请输入昵称"
              />
            </uv-form-item>
            <view class="mt-[80rpx]">
              <button
                class="bg-primary rounded-full text-white text-lg h-[80rpx] leading-[80rpx]"
                hover-class="none"
                @click="handleSubmit"
              >
                确定
              </button>
            </view>

            <view class="flex justify-center mt-[60rpx]">
              <view
                class="text-muted"
                @click="showPopup = false"
              >
                暂不登录
              </view>
            </view>
          </uv-form>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script lang="ts" setup>
import { reactive, computed, ref } from 'vue'
const props = defineProps({
  show: {
    type: Boolean
  },
  logo: {
    type: String
  },
  title: {
    type: String
  }
})
const emit = defineEmits<{
  (event: 'update:show', show: boolean): void
  (event: 'update', value: any): void
}>()

const formRef = ref()
const formData = reactive({
  avatar: null,
  nickname: ''
})
const rules = reactive({
  username: [
    {
      required: true,
      message: '手机号/账号不能为空',
      trigger: ['blur', 'change']
    }
  ],
  password: [
    {
      required: true,
      message: '密码不能为空',
      trigger: ['blur', 'change']
    }
  ]
})

const showPopup = computed({
  get() {
    return props.show
  },
  set(val) {
    emit('update:show', val)
  }
})

const handleSubmit = () => {
  const { avatar, nickname } = formData
  if (!avatar) return uni.$uv.toast('请添加头像')
  if (!nickname) return uni.$uv.toast('请输入昵称')
  emit('update', {
    avatar,
    nickname
  })

  uni.$uv.toast('信息维护成功，请重新登录')
}
</script>

<style lang="scss" scoped></style>
