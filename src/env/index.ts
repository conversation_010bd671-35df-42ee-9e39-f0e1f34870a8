import { BASE_URL as PROD_BASE_URL } from '@/env/prod'
import { BASE_URL as DEV_BASE_URL } from '@/env/dev'
import { BASE_URL as TRIAL_BASE_URL } from '@/env/trial'

// 获取环境信息，兼容不同平台
let env: string | undefined

// #ifdef MP-WEIXIN
try {
  env = uni?.getAccountInfoSync()?.miniProgram?.envVersion
} catch (error) {
  console.warn('获取小程序环境信息失败:', error)
}
// #endif

// #ifdef H5
// H5 环境下根据当前域名或环境变量判断
const hostname = window?.location?.hostname || ''
if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
  env = 'develop'
} else if (hostname.includes('test') || hostname.includes('trial')) {
  env = 'trial'
} else {
  env = 'release'
}
// #endif

// #ifndef MP-WEIXIN || H5
// 其他平台默认使用生产环境
env = 'release'
// #endif

const baseEnvUrl = env === 'develop' ? DEV_BASE_URL : env === 'trial' ? TRIAL_BASE_URL : PROD_BASE_URL
console.log('当前环境:', env)
console.log('当前环境API地址:', baseEnvUrl.api_url)

const apiOptions: any = {
  baseUrl: baseEnvUrl.api_url
}

export default apiOptions
